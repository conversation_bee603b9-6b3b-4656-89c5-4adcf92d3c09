import 'package:objectbox/objectbox.dart';

/// Session entity for storing user session information
/// UID range: 1001-1009
@Entity()
class SessionEntity {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  /// Unique session key for identification
  @Unique()
  @Property(uid: 1001)
  String sessionKey = '';

  /// Session ID from server
  @Property(uid: 1002)
  String sessionId = '';

  /// User ID associated with this session
  @Property(uid: 1003)
  String userId = '';

  /// When the user logged in
  @Property(uid: 1004)
  DateTime loginTime = DateTime.now();

  /// When the user logged out (null if still active)
  @Property(uid: 1005)
  DateTime? logoutTime;

  /// Device information for this session
  @Property(uid: 1006)
  String deviceInfo = '';

  /// IP address of the session
  @Property(uid: 1007)
  String? ipAddress;

  /// Whether this session is currently active
  @Property(uid: 1008)
  bool isActive = true;

  /// Last update time for this session
  @Property(uid: 1009)
  DateTime updateTime = DateTime.now();

  /// Default constructor
  SessionEntity();

  /// Constructor with required fields
  SessionEntity.create({
    required this.sessionKey,
    required this.sessionId,
    required this.userId,
    required this.deviceInfo,
    this.ipAddress,
    DateTime? loginTime,
    DateTime? updateTime,
  }) {
    this.loginTime = loginTime ?? DateTime.now();
    this.updateTime = updateTime ?? DateTime.now();
  }

  /// Copy constructor for updates
  SessionEntity copyWith({
    int? id,
    String? sessionKey,
    String? sessionId,
    String? userId,
    DateTime? loginTime,
    DateTime? logoutTime,
    String? deviceInfo,
    String? ipAddress,
    bool? isActive,
    DateTime? updateTime,
  }) {
    return SessionEntity()
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..sessionId = sessionId ?? this.sessionId
      ..userId = userId ?? this.userId
      ..loginTime = loginTime ?? this.loginTime
      ..logoutTime = logoutTime ?? this.logoutTime
      ..deviceInfo = deviceInfo ?? this.deviceInfo
      ..ipAddress = ipAddress ?? this.ipAddress
      ..isActive = isActive ?? this.isActive
      ..updateTime = updateTime ?? this.updateTime;
  }

  /// Mark session as logged out
  void logout() {
    isActive = false;
    logoutTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Update session activity
  void updateActivity() {
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'SessionEntity{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'sessionId: $sessionId, '
        'userId: $userId, '
        'loginTime: $loginTime, '
        'logoutTime: $logoutTime, '
        'deviceInfo: $deviceInfo, '
        'ipAddress: $ipAddress, '
        'isActive: $isActive, '
        'updateTime: $updateTime'
        '}';
  }
}
