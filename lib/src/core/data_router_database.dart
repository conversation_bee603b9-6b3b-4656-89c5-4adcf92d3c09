import 'dart:io';
import 'package:injectable/injectable.dart';
import '../data/database/entities/session/session_entity.dart';
import '../shared/types/result.dart';
import '../../objectbox.g.dart';

/// Main database class for DataRouter
/// Manages ObjectBox store and provides database operations
@LazySingleton()
class DataRouterDatabase {
  Store? _store;

  /// Get the ObjectBox store instance
  Store get store {
    if (_store == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _store!;
  }

  /// Check if database is initialized
  bool get isInitialized => _store != null;

  /// Initialize the database
  /// [directory] - Optional directory path for database files
  /// If null, uses default directory
  Future<Result<void>> initialize({String? directory}) async {
    try {
      if (_store != null) {
        return const Success(null);
      }

      // Create ObjectBox store
      _store = await openStore(directory: directory);

      // Initialize admin user if needed
      await _initializeAdminUser();

      return const Success(null);
    } catch (e, stackTrace) {
      return Failure(
        'Failed to initialize database: ${e.toString()}',
        Exception(
          'Database initialization error: $e\nStack trace: $stackTrace',
        ),
      );
    }
  }

  /// Close the database
  Future<Result<void>> close() async {
    try {
      if (_store != null) {
        _store!.close();
        _store = null;
      }
      return const Success(null);
    } catch (e) {
      return Failure(
        'Failed to close database: ${e.toString()}',
        Exception('Database close error: $e'),
      );
    }
  }

  /// Clear all data from database (for development/testing)
  Future<Result<void>> clearAllData() async {
    try {
      if (_store == null) {
        return const Failure('Database not initialized');
      }

      // Clear session box
      final sessionBox = _store!.box<SessionEntity>();
      sessionBox.removeAll();

      return const Success(null);
    } catch (e) {
      return Failure(
        'Failed to clear database: ${e.toString()}',
        Exception('Database clear error: $e'),
      );
    }
  }

  /// Get database statistics
  Future<Result<Map<String, dynamic>>> getStats() async {
    try {
      if (_store == null) {
        return const Failure('Database not initialized');
      }

      final stats = <String, dynamic>{};

      // Get session count
      final sessionBox = _store!.box<SessionEntity>();
      stats['sessions'] = {
        'total': sessionBox.count(),
        'active': sessionBox
            .query(SessionEntity_.isActive.equals(true))
            .build()
            .count(),
      };

      // Add database info
      stats['database'] = {
        'path': _store!.directoryPath,
        'size': await _getDatabaseSize(),
      };

      return Success(stats);
    } catch (e) {
      return Failure(
        'Failed to get database stats: ${e.toString()}',
        Exception('Database stats error: $e'),
      );
    }
  }

  /// Initialize admin user session for development
  Future<void> _initializeAdminUser() async {
    try {
      final sessionBox = store.box<SessionEntity>();

      // Check if admin session already exists
      final existingAdminSession = sessionBox
          .query(SessionEntity_.userId.equals('admin'))
          .build()
          .findFirst();

      if (existingAdminSession == null) {
        // Create admin session
        final adminSession = SessionEntity.create(
          sessionKey: 'admin_session_key',
          sessionId: 'admin_session_001',
          userId: 'admin',
          deviceInfo: 'Development Admin Device',
          ipAddress: '127.0.0.1',
        );

        sessionBox.put(adminSession);
        print('✅ Admin user session created successfully');
      } else {
        print('✅ Admin user session already exists');
      }
    } catch (e) {
      print('❌ Failed to initialize admin user: $e');
      rethrow;
    }
  }

  /// Get database size in bytes
  Future<int> _getDatabaseSize() async {
    try {
      final directory = Directory(store.directoryPath);
      if (!directory.existsSync()) return 0;

      int totalSize = 0;
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
      return totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Get formatted database size
  String getFormattedDatabaseSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
