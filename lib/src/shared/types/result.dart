/// Result type for handling success and failure cases
sealed class Result<T> {
  const Result();
}

/// Success result containing data
class Success<T> extends Result<T> {
  final T data;
  const Success(this.data);
  
  @override
  String toString() => 'Success(data: $data)';
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Success<T> && runtimeType == other.runtimeType && data == other.data;
  
  @override
  int get hashCode => data.hashCode;
}

/// Failure result containing error message and optional exception
class Failure<T> extends Result<T> {
  final String message;
  final Exception? exception;
  
  const Failure(this.message, [this.exception]);
  
  @override
  String toString() => 'Failure(message: $message, exception: $exception)';
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Failure<T> && 
      runtimeType == other.runtimeType && 
      message == other.message;
  
  @override
  int get hashCode => message.hashCode ^ exception.hashCode;
}

/// Extension methods for easier usage of Result
extension ResultExtensions<T> on Result<T> {
  /// Check if result is success
  bool get isSuccess => this is Success<T>;
  
  /// Check if result is failure
  bool get isFailure => this is Failure<T>;
  
  /// Get data if success, null otherwise
  T? get dataOrNull => isSuccess ? (this as Success<T>).data : null;
  
  /// Get error message if failure, null otherwise
  String? get errorMessage => isFailure ? (this as Failure<T>).message : null;
  
  /// Fold result into a single value
  R fold<R>({
    required R Function(T data) onSuccess,
    required R Function(String message, Exception? exception) onFailure,
  }) {
    return switch (this) {
      Success<T>(:final data) => onSuccess(data),
      Failure<T>(:final message, :final exception) => onFailure(message, exception),
    };
  }
  
  /// Map the data if success, keep failure unchanged
  Result<R> map<R>(R Function(T data) mapper) {
    return switch (this) {
      Success<T>(:final data) => Success(mapper(data)),
      Failure<T>(:final message, :final exception) => Failure(message, exception),
    };
  }
  
  /// FlatMap for chaining operations
  Result<R> flatMap<R>(Result<R> Function(T data) mapper) {
    return switch (this) {
      Success<T>(:final data) => mapper(data),
      Failure<T>(:final message, :final exception) => Failure(message, exception),
    };
  }
}
