{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4965594264936917621", "lastPropertyId": "10:1009", "name": "SessionEntity", "properties": [{"id": "1:4318502882168555349", "name": "id", "type": 6, "flags": 129}, {"id": "2:1001", "name": "<PERSON><PERSON><PERSON>", "type": 9, "flags": 2080, "indexId": "1:1980291022318617519"}, {"id": "3:1002", "name": "sessionId", "type": 9}, {"id": "4:1003", "name": "userId", "type": 9}, {"id": "5:1004", "name": "loginTime", "type": 10}, {"id": "6:1005", "name": "logoutTime", "type": 10}, {"id": "7:1006", "name": "deviceInfo", "type": 9}, {"id": "8:1007", "name": "ip<PERSON><PERSON><PERSON>", "type": 9}, {"id": "9:1008", "name": "isActive", "type": 1}, {"id": "10:1009", "name": "updateTime", "type": 10}], "relations": []}], "lastEntityId": "1:4965594264936917621", "lastIndexId": "1:1980291022318617519", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}