name: data_router
description: Local-first data router with Clean Architecture
version: 1.0.0
publish_to: none

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # Database essentials
  objectbox: ^2.0.0
  objectbox_flutter_libs: ^2.0.0

  # Dependency injection
  injectable: ^2.1.0
  get_it: ^7.6.0

  # Utilities
  equatable: ^2.0.5
  rxdart: ^0.27.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code generation
  objectbox_generator: ^2.0.0
  build_runner: ^2.4.0
  injectable_generator: ^2.4.1

  # Testing
  test: ^1.24.0
  mockito: ^5.4.0
  mocktail: ^1.0.4

  # Linting
  flutter_lints: ^3.0.0
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true
