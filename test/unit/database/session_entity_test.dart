import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/data_router.dart';

void main() {
  group('SessionEntity Tests', () {
    test('should create session entity with required properties', () {
      // Arrange & Act
      final session = SessionEntity.create(
        sessionKey: 'test_session_key',
        sessionId: 'test_session_001',
        userId: 'test_user_123',
        deviceInfo: 'Test Device',
        ipAddress: '127.0.0.1',
      );

      // Assert
      expect(session.sessionKey, equals('test_session_key'));
      expect(session.sessionId, equals('test_session_001'));
      expect(session.userId, equals('test_user_123'));
      expect(session.deviceInfo, equals('Test Device'));
      expect(session.ipAddress, equals('127.0.0.1'));
      expect(session.isActive, isTrue);
      expect(session.logoutTime, isNull);
    });

    test('should create session entity with default constructor', () {
      // Arrange & Act
      final session = SessionEntity();

      // Assert
      expect(session.sessionKey, equals(''));
      expect(session.sessionId, equals(''));
      expect(session.userId, equals(''));
      expect(session.deviceInfo, equals(''));
      expect(session.isActive, isTrue);
      expect(session.logoutTime, isNull);
    });

    test('should copy session entity with updated properties', () {
      // Arrange
      final originalSession = SessionEntity.create(
        sessionKey: 'original_key',
        sessionId: 'original_id',
        userId: 'original_user',
        deviceInfo: 'Original Device',
      );

      // Act
      final copiedSession = originalSession.copyWith(
        sessionKey: 'updated_key',
        userId: 'updated_user',
        isActive: false,
      );

      // Assert
      expect(copiedSession.sessionKey, equals('updated_key'));
      expect(copiedSession.sessionId, equals('original_id')); // unchanged
      expect(copiedSession.userId, equals('updated_user'));
      expect(copiedSession.deviceInfo, equals('Original Device')); // unchanged
      expect(copiedSession.isActive, isFalse);
    });

    test('should logout session correctly', () {
      // Arrange
      final session = SessionEntity.create(
        sessionKey: 'test_key',
        sessionId: 'test_id',
        userId: 'test_user',
        deviceInfo: 'Test Device',
      );
      final beforeLogout = DateTime.now();

      // Act
      session.logout();
      final afterLogout = DateTime.now();

      // Assert
      expect(session.isActive, isFalse);
      expect(session.logoutTime, isNotNull);
      expect(session.logoutTime!.isAfter(beforeLogout), isTrue);
      expect(session.logoutTime!.isBefore(afterLogout), isTrue);
    });

    test('should update activity correctly', () async {
      // Arrange
      final session = SessionEntity.create(
        sessionKey: 'test_key',
        sessionId: 'test_id',
        userId: 'test_user',
        deviceInfo: 'Test Device',
      );
      final originalUpdateTime = session.updateTime;

      // Wait a bit to ensure time difference
      await Future.delayed(const Duration(milliseconds: 10));

      // Act
      session.updateActivity();

      // Assert
      expect(session.updateTime.isAfter(originalUpdateTime), isTrue);
    });

    test('should have proper toString representation', () {
      // Arrange
      final session = SessionEntity.create(
        sessionKey: 'test_key',
        sessionId: 'test_id',
        userId: 'test_user',
        deviceInfo: 'Test Device',
        ipAddress: '***********',
      );

      // Act
      final stringRepresentation = session.toString();

      // Assert
      expect(stringRepresentation, contains('SessionEntity{'));
      expect(stringRepresentation, contains('sessionKey: test_key'));
      expect(stringRepresentation, contains('sessionId: test_id'));
      expect(stringRepresentation, contains('userId: test_user'));
      expect(stringRepresentation, contains('deviceInfo: Test Device'));
      expect(stringRepresentation, contains('ipAddress: ***********'));
      expect(stringRepresentation, contains('isActive: true'));
    });

    test('should handle null optional properties', () {
      // Arrange & Act
      final session = SessionEntity.create(
        sessionKey: 'test_key',
        sessionId: 'test_id',
        userId: 'test_user',
        deviceInfo: 'Test Device',
        // ipAddress is null
      );

      // Assert
      expect(session.ipAddress, isNull);
      expect(session.logoutTime, isNull);
    });

    test('should set custom login and update times', () {
      // Arrange
      final customLoginTime = DateTime(2024, 1, 1, 12, 0, 0);
      final customUpdateTime = DateTime(2024, 1, 1, 12, 30, 0);

      // Act
      final session = SessionEntity.create(
        sessionKey: 'test_key',
        sessionId: 'test_id',
        userId: 'test_user',
        deviceInfo: 'Test Device',
        loginTime: customLoginTime,
        updateTime: customUpdateTime,
      );

      // Assert
      expect(session.loginTime, equals(customLoginTime));
      expect(session.updateTime, equals(customUpdateTime));
    });
  });
}
