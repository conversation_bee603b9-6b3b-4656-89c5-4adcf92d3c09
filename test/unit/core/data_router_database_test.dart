import 'package:flutter_test/flutter_test.dart';
import 'package:data_router/data_router.dart';

void main() {
  group('DataRouterDatabase Tests', () {
    late DataRouterDatabase database;
    
    setUp(() {
      database = DataRouterDatabase();
    });
    
    tearDown(() async {
      if (database.isInitialized) {
        await database.close();
      }
    });
    
    test('should not be initialized by default', () {
      // Assert
      expect(database.isInitialized, isFalse);
    });
    
    test('should throw StateError when accessing store before initialization', () {
      // Act & Assert
      expect(() => database.store, throwsStateError);
    });
    
    test('should return success result type', () {
      // Arrange
      const result = Success('test data');
      
      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.isFailure, isFalse);
      expect(result.dataOrNull, equals('test data'));
      expect(result.errorMessage, isNull);
    });
    
    test('should return failure result type', () {
      // Arrange
      const result = Failure<String>('error message');
      
      // Assert
      expect(result.isFailure, isTrue);
      expect(result.isSuccess, isFalse);
      expect(result.dataOrNull, isNull);
      expect(result.errorMessage, equals('error message'));
    });
    
    test('should fold result correctly', () {
      // Arrange
      const successResult = Success(42);
      const failureResult = Failure<int>('error');
      
      // Act
      final successValue = successResult.fold(
        onSuccess: (data) => 'Success: $data',
        onFailure: (message, exception) => 'Failure: $message',
      );
      
      final failureValue = failureResult.fold(
        onSuccess: (data) => 'Success: $data',
        onFailure: (message, exception) => 'Failure: $message',
      );
      
      // Assert
      expect(successValue, equals('Success: 42'));
      expect(failureValue, equals('Failure: error'));
    });
    
    test('should map result correctly', () {
      // Arrange
      const successResult = Success(5);
      const failureResult = Failure<int>('error');
      
      // Act
      final mappedSuccess = successResult.map((data) => data * 2);
      final mappedFailure = failureResult.map((data) => data * 2);
      
      // Assert
      expect(mappedSuccess.isSuccess, isTrue);
      expect(mappedSuccess.dataOrNull, equals(10));
      
      expect(mappedFailure.isFailure, isTrue);
      expect(mappedFailure.errorMessage, equals('error'));
    });
    
    test('should flatMap result correctly', () {
      // Arrange
      const successResult = Success(5);
      const failureResult = Failure<int>('error');
      
      // Act
      final flatMappedSuccess = successResult.flatMap((data) => Success(data * 2));
      final flatMappedFailure = failureResult.flatMap((data) => Success(data * 2));
      final flatMappedToFailure = successResult.flatMap((data) => const Failure<int>('new error'));
      
      // Assert
      expect(flatMappedSuccess.isSuccess, isTrue);
      expect(flatMappedSuccess.dataOrNull, equals(10));
      
      expect(flatMappedFailure.isFailure, isTrue);
      expect(flatMappedFailure.errorMessage, equals('error'));
      
      expect(flatMappedToFailure.isFailure, isTrue);
      expect(flatMappedToFailure.errorMessage, equals('new error'));
    });
    
    test('should handle database size formatting', () {
      // Act & Assert
      expect(database.getFormattedDatabaseSize(500), equals('500 B'));
      expect(database.getFormattedDatabaseSize(1536), equals('1.5 KB'));
      expect(database.getFormattedDatabaseSize(2097152), equals('2.0 MB'));
      expect(database.getFormattedDatabaseSize(1073741824), equals('1.0 GB'));
    });
    
    test('should handle Result equality correctly', () {
      // Arrange
      const success1 = Success('test');
      const success2 = Success('test');
      const success3 = Success('different');
      const failure1 = Failure<String>('error');
      const failure2 = Failure<String>('error');
      const failure3 = Failure<String>('different error');
      
      // Assert
      expect(success1, equals(success2));
      expect(success1, isNot(equals(success3)));
      expect(failure1, equals(failure2));
      expect(failure1, isNot(equals(failure3)));
      expect(success1, isNot(equals(failure1)));
    });
    
    test('should handle Result hashCode correctly', () {
      // Arrange
      const success1 = Success('test');
      const success2 = Success('test');
      const failure1 = Failure<String>('error');
      const failure2 = Failure<String>('error');
      
      // Assert
      expect(success1.hashCode, equals(success2.hashCode));
      expect(failure1.hashCode, equals(failure2.hashCode));
    });
    
    test('should handle Result toString correctly', () {
      // Arrange
      const success = Success('test data');
      const failure = Failure<String>('error message');
      
      // Assert
      expect(success.toString(), equals('Success(data: test data)'));
      expect(failure.toString(), contains('Failure(message: error message'));
    });
  });
}
