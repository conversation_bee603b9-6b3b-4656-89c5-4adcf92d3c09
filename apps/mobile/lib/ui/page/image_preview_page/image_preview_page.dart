import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

@RoutePage()
class ImagePreviewPage extends StatefulWidget {
  const ImagePreviewPage({
    required this.imagePath,
    super.key,
  });

  final String imagePath;

  @override
  State<ImagePreviewPage> createState() => _ImagePreviewPageState();
}

class _ImagePreviewPageState extends State<ImagePreviewPage> {
  @override
  Widget build(BuildContext context) {
    return PreviewMessagePhotoPage(
      image: FileImage(File(widget.imagePath)),
      onBackButtonClicked: _onBack,
      onUploadButtonClicked: _onUpload,
    );
  }

  void _onBack() {
    context.maybePop(false);
  }

  void _onUpload() {
    context.maybePop(true);
    AppEventBus.publish(
      TakePhotoMessageEvent(
        id: 'TakePhotoMessageID',
        filePath: widget.imagePath,
      ),
    );
  }
}
