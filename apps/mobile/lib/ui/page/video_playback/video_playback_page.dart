import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

@RoutePage()
class VideoPlaybackPage extends StatelessWidget {
  const VideoPlaybackPage({
    required this.videoPath,
    super.key,
  });

  final String videoPath;

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: ui.VideoPlaybackPage(
        videoPath: videoPath,
        onClose: () => _onBack(context),
        onUpload: () => _onUploadVideo(context),
      ),
    );
  }

  void _onBack(BuildContext context) {
    context.maybePop(false);
  }

  Future<void> _onUploadVideo(BuildContext context) async {
    AppEventBus.publish(
      TakeVideoMessageEvent(
        id: 'TakeVideoMessageID',
        filePath: videoPath,
      ),
    );
    AppEventBus.publish(PopToChannelViewEvent());
  }
}
