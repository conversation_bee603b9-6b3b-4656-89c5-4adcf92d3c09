import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:user_manager/user_manager.dart' as user;

import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class BlockUsersPage extends StatefulWidget {
  BlockUsersPage({
    super.key,
  });

  @override
  State<BlockUsersPage> createState() => _BlockUsersPageState();
}

class _BlockUsersPageState extends State<BlockUsersPage> {
  @override
  Widget build(BuildContext context) {
    return user.BlockUserPage(
      onOpenUserProfile: openUserProfile,
    );
  }

  void openUserProfile(userId) {
    context.router.push(UserProfileRoute(userId: userId));
  }
}
