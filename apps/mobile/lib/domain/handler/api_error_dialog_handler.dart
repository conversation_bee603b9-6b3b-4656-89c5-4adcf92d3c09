import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../navigation/routes/app_router.dart';

class ApiErrorDialogHandler {
  StreamSubscription? _subscription;
  bool _isDialogVisible = false;

  void register(BuildContext context) {
    _subscription = GetIt.instance
        .get<AppEventBus>()
        .on<OnShowApiErrorDialog>()
        .listen((event) {
      _showDialog(context, event.errorType);
    });
  }

  void cancel() {
    _subscription?.cancel();
    _subscription = null;
  }

  void _showDialog(BuildContext context, ApiErrorTypeEnum errorType) {
    if (_isDialogVisible) return;

    _isDialogVisible = true;
    switch (errorType) {
      case ApiErrorTypeEnum.lacksPermission:
        _showLacksPermissionDialog(context);
        break;
      case ApiErrorTypeEnum.blockedUser:
      case ApiErrorTypeEnum.unknown:
        _showAccountUnavailableDialog(context);
    }
  }

  void _showAccountUnavailableDialog(BuildContext context) {
    DialogUtils.showAccountUnavailableDialog(
      context,
      barrierDismissible: false,
      onFirstAction: (ctx) {
        var router = GetIt.instance.get<AppRouter>();
        router.popUtilOrReplace(router.current.name);
        _isDialogVisible = false;
      },
    );
  }

  void _showLacksPermissionDialog(BuildContext context) {
    DialogUtils.showCannotTransferOwner(
      context,
      barrierDismissible: false,
      onOKClicked: (ctx) {
        var router = GetIt.instance.get<AppRouter>();
        router.popUtilOrReplace(router.current.name);
        _isDialogVisible = false;
      },
    );
  }
}
