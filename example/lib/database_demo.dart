import 'package:flutter/material.dart';
import 'package:data_router/data_router.dart';

/// Database demo widget to test DataRouter database functionality
class DatabaseDemo extends StatefulWidget {
  const DatabaseDemo({super.key});

  @override
  State<DatabaseDemo> createState() => _DatabaseDemoState();
}

class _DatabaseDemoState extends State<DatabaseDemo> {
  final DataRouterDatabase _database = DataRouterDatabase();
  bool _isInitialized = false;
  bool _isLoading = false;
  String _statusMessage = 'Database not initialized';
  Map<String, dynamic>? _stats;
  List<SessionEntity> _sessions = [];

  @override
  void initState() {
    super.initState();
    _initializeDatabase();
  }

  @override
  void dispose() {
    _database.close();
    super.dispose();
  }

  Future<void> _initializeDatabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Initializing database...';
    });

    final result = await _database.initialize();
    
    result.fold(
      onSuccess: (_) {
        setState(() {
          _isInitialized = true;
          _isLoading = false;
          _statusMessage = 'Database initialized successfully!';
        });
        _loadStats();
        _loadSessions();
      },
      onFailure: (message, exception) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Failed to initialize database: $message';
        });
      },
    );
  }

  Future<void> _loadStats() async {
    if (!_isInitialized) return;

    final result = await _database.getStats();
    result.fold(
      onSuccess: (stats) {
        setState(() {
          _stats = stats;
        });
      },
      onFailure: (message, exception) {
        setState(() {
          _statusMessage = 'Failed to load stats: $message';
        });
      },
    );
  }

  Future<void> _loadSessions() async {
    if (!_isInitialized) return;

    try {
      final sessionBox = _database.store.box<SessionEntity>();
      final sessions = sessionBox.getAll();
      setState(() {
        _sessions = sessions;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Failed to load sessions: $e';
      });
    }
  }

  Future<void> _createTestSession() async {
    if (!_isInitialized) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Creating test session...';
    });

    try {
      final sessionBox = _database.store.box<SessionEntity>();
      final testSession = SessionEntity.create(
        sessionKey: 'test_session_${DateTime.now().millisecondsSinceEpoch}',
        sessionId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
        deviceInfo: 'Flutter Test Device',
        ipAddress: '*************',
      );

      sessionBox.put(testSession);
      
      setState(() {
        _isLoading = false;
        _statusMessage = 'Test session created successfully!';
      });
      
      _loadStats();
      _loadSessions();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Failed to create test session: $e';
      });
    }
  }

  Future<void> _clearAllData() async {
    if (!_isInitialized) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Clearing all data...';
    });

    final result = await _database.clearAllData();
    
    result.fold(
      onSuccess: (_) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'All data cleared successfully!';
        });
        _loadStats();
        _loadSessions();
      },
      onFailure: (message, exception) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Failed to clear data: $message';
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _isInitialized ? Icons.check_circle : Icons.error,
                        color: _isInitialized ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Database Status',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(_statusMessage),
                  if (_isLoading) ...[
                    const SizedBox(height: 8),
                    const LinearProgressIndicator(),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Stats Card
          if (_stats != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Database Statistics',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Total Sessions: ${_stats!['sessions']['total']}'),
                    Text('Active Sessions: ${_stats!['sessions']['active']}'),
                    Text('Database Path: ${_stats!['database']['path']}'),
                    Text('Database Size: ${_database.getFormattedDatabaseSize(_stats!['database']['size'])}'),
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _database.admin != null ? Icons.admin_panel_settings : Icons.admin_panel_settings_outlined,
                          color: _database.admin != null ? Colors.blue : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'ObjectBox Admin: ${_database.admin != null ? "Running at http://127.0.0.1:8090" : "Not available"}',
                          style: TextStyle(
                            color: _database.admin != null ? Colors.blue : Colors.grey,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isInitialized && !_isLoading ? _createTestSession : null,
                  icon: const Icon(Icons.add),
                  label: const Text('Create Test Session'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isInitialized && !_isLoading ? _clearAllData : null,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear All Data'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Sessions List
          const Text(
            'Sessions',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _sessions.isEmpty
                ? const Center(
                    child: Text('No sessions found'),
                  )
                : ListView.builder(
                    itemCount: _sessions.length,
                    itemBuilder: (context, index) {
                      final session = _sessions[index];
                      return Card(
                        child: ListTile(
                          leading: Icon(
                            session.isActive ? Icons.circle : Icons.circle_outlined,
                            color: session.isActive ? Colors.green : Colors.grey,
                          ),
                          title: Text('User: ${session.userId}'),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Session: ${session.sessionId}'),
                              Text('Device: ${session.deviceInfo}'),
                              Text('Login: ${session.loginTime.toString().split('.')[0]}'),
                            ],
                          ),
                          isThreeLine: true,
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
