import 'package:flutter/material.dart';
import 'database_demo.dart';

void main() {
  runApp(const DataRouterExampleApp());
}

/// Example Flutter app demonstrating DataRouter package usage
class DataRouterExampleApp extends StatelessWidget {
  const DataRouterExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DataRouter Example',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const DataRouterHomePage(),
    );
  }
}

/// Home page demonstrating DataRouter functionality
class DataRouterHomePage extends StatelessWidget {
  const DataRouterHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('DataRouter Database Demo'),
      ),
      body: const DatabaseDemo(),
    );
  }
}
