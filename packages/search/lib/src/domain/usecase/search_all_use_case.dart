import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:search_api/search_api.dart';
import 'package:shared/shared.dart';

import '../../common/config/config.dart';
import '../../data/repositories/database/entities/search.dart';
import '../../data/repositories/source/api/client/clients.dart';

part 'search_all_use_case.freezed.dart';

@Injectable()
class SearchAllUseCase
    extends BaseFutureUseCase<SearchAllInput, SearchAllOutput> {
  const SearchAllUseCase();

  @override
  Future<SearchAllOutput> buildUseCase(
    SearchAllInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      V3SearchEverythingRequestBuilder request =
          V3SearchEverythingRequestBuilder()..keyword = input.keyword;
      final result =
          await SearchClient().instance.searchEverything(body: request.build());
      if (result.data?.ok ?? false) {
        List<Search> searchResults = [];
        final apiChannels = result.data!.channels!.toList();
        final apiUsers = result.data!.users!.toList();
        for (final apiUser in apiUsers) {
          final result = Search.user(
            sessionKey: sessionKey!,
            userId: apiUser.userId!,
            embed: jsonEncode(
              {
                'avatar': apiUser.avatar,
                'aliasName': null,
                'displayName': apiUser.displayName,
                'username': apiUser.username,
                'userBadgeType': apiUser.userBadgeType,
              },
            ),
          );
          searchResults.add(result);
        }
        for (final channel in apiChannels) {
          final result = Search.channel(
            sessionKey: sessionKey!,
            channelId: channel.channelId!,
            workspaceId: channel.workspaceId!,
            embed: jsonEncode(
              {
                'avatar': channel.avatar,
                'channelName': channel.name,
              },
            ),
          );
          searchResults.add(result);
        }
        return SearchAllOutput(searchResults: searchResults);
      }
      return SearchAllOutput(searchResults: []);
    } on Exception catch (_) {
      return SearchAllOutput(searchResults: []);
    }
  }
}

@freezed
sealed class SearchAllInput extends BaseInput with _$SearchAllInput {
  const SearchAllInput._();
  factory SearchAllInput({required String keyword}) = _SearchAllInput;
}

@freezed
sealed class SearchAllOutput extends BaseOutput with _$SearchAllOutput {
  const SearchAllOutput._();
  factory SearchAllOutput({
    @Default([]) List<Search> searchResults,
  }) = _SearchAllOutput;
}
