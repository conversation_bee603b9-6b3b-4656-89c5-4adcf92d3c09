import 'dart:convert';

import 'package:objectbox/objectbox.dart';

import '../enums/search_type.dart';
import 'embed.dart';

@Entity()
class Search {
  Search({
    required this.sessionKey,
    this.timestamp,
    this.userId,
    this.channelId,
    this.workspaceId,
    this.embed,
    this.searchType,
    this.id = 0,
  });

  factory Search.user({
    required String sessionKey,
    required String userId,
    required String embed,
    String? searchType,
    int? timestamp,
  }) {
    return Search(
      sessionKey: sessionKey,
      timestamp: timestamp,
      userId: userId,
      embed: embed,
      searchType: searchType,
    );
  }

  factory Search.channel({
    required String sessionKey,
    required String channelId,
    required String workspaceId,
    required String embed,
    String? searchType,
    int? timestamp,
  }) {
    return Search(
      sessionKey: sessionKey,
      timestamp: timestamp,
      channelId: channelId,
      workspaceId: workspaceId,
      embed: embed,
      searchType: searchType,
    );
  }

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 2001)
  String sessionKey;

  @Property(uid: 2002)
  String? userId;

  @Property(uid: 2003)
  String? channelId;

  @Property(uid: 2004)
  String? workspaceId;

  @Property(uid: 2005)
  String? searchType;

  @Property(uid: 2006)
  String? embed;

  @Property(uid: 2007)
  int? timestamp;

  @Transient()
  SearchTypeEnum? get type => SearchTypeEnum.fromName(searchType);

  @Transient()
  Embed get _embed => Embed.fromJson(jsonDecode(embed ?? ''));

  @Transient()
  String? get avatar => _embed.avatar;

  @Transient()
  int? get userBadgeType => _embed.userBadgeType;

  @Transient()
  String get searchResultName {
    if (!isUser) {
      return _embed.channelName ?? '';
    }
    if (_embed.aliasName?.isNotEmpty ?? false) {
      return _embed.aliasName!;
    }
    if (_embed.displayName?.isNotEmpty ?? false) {
      return _embed.displayName!;
    }
    return _embed.username ?? '';
  }

  @Transient()
  String get userDisplayName {
    if (!isUser) {
      return _embed.channelName ?? '';
    }
    if (_embed.aliasName?.isNotEmpty ?? false) {
      return _embed.aliasName!;
    }
    if (_embed.displayName?.isNotEmpty ?? false) {
      return _embed.displayName!;
    }
    return '';
  }

  @Transient()
  String get username {
    if (!isUser) {
      return '';
    }
    return _embed.username ?? '';
  }

  @Transient()
  String get aliasName {
    if (!isUser) {
      return '';
    }
    return _embed.aliasName ?? '';
  }

  @Transient()
  bool get isUser => channelId == null;

  @override
  String toString() {
    return 'Search{id: $id, sessionKey: $sessionKey, userId: $userId, channelId: $channelId, workspaceId: $workspaceId, timestamp: $timestamp}';
  }
}
