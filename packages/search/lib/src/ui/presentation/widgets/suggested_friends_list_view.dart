import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../data/repositories/database/entities/search.dart';
import '../../../domain/usecase/get_suggested_friends_by_type_use_case.dart';
import '../../bloc/search_bloc.dart';

class SuggestedFriendsListView extends StatefulWidget {
  const SuggestedFriendsListView({
    required this.suggestionType,
    required this.onTapSearchItem,
    required this.pagingController,
    super.key,
  });

  final SuggestionType suggestionType;
  final void Function(Search item) onTapSearchItem;
  final PagingController<int, Search> pagingController;

  @override
  State<SuggestedFriendsListView> createState() =>
      SuggestedFriendsListViewState();
}

class SuggestedFriendsListViewState extends State<SuggestedFriendsListView> {
  String? _nextPageToken;
  int _pageKey = 0;
  List<UserPrivateData> _listUserPrivateData = [];

  @override
  void initState() {
    super.initState();
    widget.pagingController.addPageRequestListener(
      (pageKey) {
        if (_pageKey != 0) {
          context.read<SearchBloc>().add(
                GetSuggestedFriendsEvent(
                  suggestionType: widget.suggestionType,
                  nextPageToken: _nextPageToken,
                ),
              );
        }
      },
    );
  }

  Future<void> onRefresh(String keyword) async {
    _nextPageToken = null;
    _pageKey = 0;
    widget.pagingController.refresh();
  }

  void _blocChannelListener(BuildContext context, SearchState state) {
    if ((widget.suggestionType == SuggestionType.channel &&
            state.isNewSameChannelUsers) ||
        (widget.suggestionType == SuggestionType.friend &&
            state.isNewUsersYouMayKnow)) {
      _appendPage(state);
    }
  }

  void _appendPage(SearchState state) {
    List<Search> searchResults = [];
    bool hasNext = false;
    if (widget.suggestionType == SuggestionType.channel) {
      searchResults = state.sameChannelUsers?.results ?? [];
      hasNext = state.sameChannelUsers?.hasNext ?? false;
      _nextPageToken = state.sameChannelUsers?.nextPageToken;
    }
    if (widget.suggestionType == SuggestionType.friend) {
      searchResults = state.usersYouMayKnow?.results ?? [];
      hasNext = state.usersYouMayKnow?.hasNext ?? false;
      _nextPageToken = state.usersYouMayKnow?.nextPageToken;
    }
    _pageKey += searchResults.length;
    if (hasNext) {
      widget.pagingController.appendPage(searchResults, _pageKey);
    } else {
      widget.pagingController.appendLastPage(searchResults);
    }
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  void _handleUserPrivateState(UserPrivateDataState state) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SearchBloc>().state;
    final userPrivateDataState = context.watch<UserPrivateDataBloc>().state;
    _handleUserPrivateState(userPrivateDataState);
    if (_pageKey == 0) {
      _appendPage(state);
    }
    return MultiBlocListener(
      listeners: [
        BlocListener<SearchBloc, SearchState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocChannelListener,
        ),
        BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserPrivateListener,
        ),
      ],
      child: PagedListView<int, Search>(
        scrollDirection: Axis.horizontal,
        pagingController: widget.pagingController,
        padding: EdgeInsets.symmetric(horizontal: 7.w),
        builderDelegate: PagedChildBuilderDelegate<Search>(
          itemBuilder: (context, item, index) {
            final model = ui.UserSearchModel(
              userId: item.userId!,
              userName: item.username,
              name: getAliasName(item.userId!) ?? item.userDisplayName,
              avatarUrl: UrlUtils.parseAvatar(item.avatar),
            );
            return ui.CardSearchWidget(
              model: model,
              onTap: () {
                widget.onTapSearchItem(item);
              },
            );
          },
          noItemsFoundIndicatorBuilder: (_) {
            return Container();
          },
          newPageProgressIndicatorBuilder: (_) {
            return Center(child: ui.AppCircularProgressIndicator());
          },
          firstPageProgressIndicatorBuilder: (_) {
            return Container();
          },
        ),
      ),
    );
  }
}
