import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/source/api/client/hash_cash_client.dart';
import '../../data/repositories/source/api/hash_cash/hash_cash.dart';

@Injectable()
class HashCashUseCase extends BaseFutureUseCase<HashCashInput, HashCashOutput> {
  HashCashUseCase();

  @override
  Future<HashCashOutput> buildUseCase(HashCashInput input) async {
    final hashCash = await _getPowChallenge();

    return HashCashOutput(hashCash: hashCash);
  }
}

Future<HashCash> _getPowChallenge() async {
  final response = await HashCashClient().instance.getPowChallenge();

  final responseData = response.data!;
  final data = responseData.data;
  final level = data?.level;
  final challenge = data?.challenge;

  if (challenge == null || level == null) {
    throw Exception('Invalid challenge or level received');
  }

  final hashCash = HashCash(challenge, level)..solveProofOfWork();

  return hashCash;
}

class HashCashInput extends BaseInput {
  HashCashInput();
}

class HashCashOutput extends BaseOutput {
  HashCashOutput({required this.hashCash});

  final HashCash hashCash;
}
