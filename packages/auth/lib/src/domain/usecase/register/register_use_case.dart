import 'package:auth_api/auth_api.dart';
import 'package:dio/src/response.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';
import '../../../data/repositories/source/api/hash_cash/hash_cash.dart';

@Injectable()
class RegisterUseCase extends BaseFutureUseCase<RegisterInput, RegisterOutput> {
  RegisterUseCase();

  @override
  Future<RegisterOutput> buildUseCase(RegisterInput input) async {
    Response<V3RegisterWithUserKeyResponse>? registerResponse =
        await _handleRegister(
      input.registerRequest,
      input.hashCash,
    );

    return RegisterOutput(response: registerResponse?.data);
  }

  Future<Response<V3RegisterWithUserKeyResponse>?> _handleRegister(
    V3RegisterWithUserKeyRequest registerRequest,
    HashCash hashCash,
  ) async {
    var requestBuilder = registerRequest.toBuilder();
    var deviceInfo = await getDeviceDetails();
    requestBuilder.deviceInfo
      ..platform = deviceInfo['platform']
      ..osVersion = deviceInfo['osVersion']
      ..deviceId = deviceInfo['deviceId']
      ..deviceName = deviceInfo['deviceName']
      ..manufacturer = deviceInfo['manufacturer']
      ..sdkVersion = deviceInfo['sdkVersion'];

    final registerResponse = await AuthClient().instance.registerWithUserKey(
          body: requestBuilder.build(),
          headers: hashCash.getHashCashMetadata(),
        );
    return registerResponse;
  }
}

class RegisterInput extends BaseInput {
  RegisterInput({
    required this.registerRequest,
    required this.hashCash,
  });

  final V3RegisterWithUserKeyRequest registerRequest;
  final HashCash hashCash;
}

class RegisterOutput extends BaseOutput {
  RegisterOutput({required this.response});

  final V3RegisterWithUserKeyResponse? response;
}
