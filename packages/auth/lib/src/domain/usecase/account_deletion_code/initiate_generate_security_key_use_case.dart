import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class InitiateGenerateSecurityKeyUseCase extends BaseFutureUseCase<
    InitiateGenerateSecurityKeyInput, InitiateGenerateSecurityKeyOutput> {
  InitiateGenerateSecurityKeyUseCase();

  @override
  Future<InitiateGenerateSecurityKeyOutput> buildUseCase(
    InitiateGenerateSecurityKeyInput input,
  ) async {
    try {
      final request = V3InitiateGenerateSecurityKeyFlowRequestBuilder()
        ..reqChallenge = input.reqChallengeHash;

      final response =
          await AuthClient().instance.initiateGenerateSecurityKeyFlow(
                body: request.build(),
              );

      return InitiateGenerateSecurityKeyOutput(
        response: response.data!,
        error: null,
      );
    } catch (e) {
      return InitiateGenerateSecurityKeyOutput(
        response: null,
        error: e.toString(),
      );
    }
  }
}

class InitiateGenerateSecurityKeyInput extends BaseInput {
  InitiateGenerateSecurityKeyInput({
    required this.reqChallengeHash,
  });

  final String reqChallengeHash;
}

class InitiateGenerateSecurityKeyOutput extends BaseOutput {
  InitiateGenerateSecurityKeyOutput({
    required this.response,
    required this.error,
  });

  final V3InitiateGenerateSecurityKeyFlowResponse? response;
  final String? error;
}
