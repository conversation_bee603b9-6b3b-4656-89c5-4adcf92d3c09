import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class GetQRAuthStateUseCase extends BaseFutureUseCase<
    GetQRAuthStateUseCaseInput, GetQRAuthStateUseCaseOutput> {
  GetQRAuthStateUseCase();

  @override
  Future<GetQRAuthStateUseCaseOutput> buildUseCase(
    GetQRAuthStateUseCaseInput input,
  ) async {
    final response =
        await AuthClient().instance.getQRAuthState(reqId: input.requestId);

    return GetQRAuthStateUseCaseOutput(response: response.data!);
  }
}

class GetQRAuthStateUseCaseInput extends BaseInput {
  GetQRAuthStateUseCaseInput({
    required this.requestId,
  });

  final String requestId;
}

class GetQRAuthStateUseCaseOutput extends BaseOutput {
  GetQRAuthStateUseCaseOutput({required this.response});

  final V3GetQRAuthStateResponse? response;
}
