import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../auth.dart';

part 'is_authenticated_use_case.freezed.dart';

@Injectable()
class IsAuthenticatedUseCase
    extends BaseSyncUseCase<IsAuthenticatedInput, IsAuthenticatedOutput> {
  const IsAuthenticatedUseCase(this._repository);

  final SessionRepository _repository;

  @protected
  @override
  IsAuthenticatedOutput buildUseCase(IsAuthenticatedInput input) {
    final session = _repository.getActiveSession();

    return IsAuthenticatedOutput(
      isAuthenticated: session != null,
      token: session != null ? session.sessionToken : '',
      userId: session != null ? session.sessionKey : '',
    );
  }
}

@freezed
sealed class IsAuthenticatedInput extends BaseInput
    with _$IsAuthenticatedInput {
  const IsAuthenticatedInput._();

  factory IsAuthenticatedInput() = _IsAuthenticatedInput;
}

@freezed
sealed class IsAuthenticatedOutput extends BaseOutput
    with _$IsAuthenticatedOutput {
  factory IsAuthenticatedOutput({
    @Default(false) bool isAuthenticated,
    @Default('') String token,
    @Default('') String userId,
  }) = _IsAuthenticatedOutput;

  const IsAuthenticatedOutput._();
}
