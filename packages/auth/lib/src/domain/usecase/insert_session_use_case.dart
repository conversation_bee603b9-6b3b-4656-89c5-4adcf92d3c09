import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../auth.dart';
import '../../data/repositories/database/entities/session_local_metadata.dart';

@Injectable()
class InsertSessionUseCase
    extends BaseFutureUseCase<InsertSessionInput, InsertSessionOutput> {
  InsertSessionUseCase(this._sessionRepository);

  final SessionRepository _sessionRepository;

  @override
  Future<InsertSessionOutput> buildUseCase(InsertSessionInput input) async {
    Session session = Session(
      sessionKey: input.userId,
      sessionId: input.sessionId,
      sessionToken: input.sessionToken,
      active: input.active ?? false,
      isLogin: input.isLogin ?? false,
      isLoginQR: input.isLoginQR ?? false,
    );
    _sessionRepository.insert(session);

    _sessionRepository.insertMetadata(
      SessionLocalMetadata(
        sessionKey: session.sessionKey,
      ),
    );
    return InsertSessionOutput();
  }
}

class InsertSessionInput extends BaseInput {
  final String sessionId;
  final String userId;
  final String sessionToken;
  final bool? active;
  final bool? isLogin;
  final bool? isLoginQR;

  InsertSessionInput({
    required this.sessionId,
    required this.userId,
    required this.sessionToken,
    this.active = true,
    this.isLogin = true,
    this.isLoginQR = false,
  });
}

class InsertSessionOutput extends BaseOutput {}
