enum Status { valid, expired, loggedOut, deleted, authRequest, active }

extension StatusExtension on Status {
  String get rawValue {
    switch (this) {
      case Status.valid:
        return 'valid';
      case Status.expired:
        return 'expired';
      case Status.loggedOut:
        return 'logged_out';
      case Status.deleted:
        return 'deleted';
      case Status.authRequest:
        return 'auth_request';
      case Status.active:
        return 'active';
    }
  }

  static Status fromRawValue(String rawValue) {
    switch (rawValue) {
      case 'valid':
        return Status.valid;
      case 'expired':
        return Status.expired;
      case 'logged_out':
        return Status.loggedOut;
      case 'deleted':
        return Status.deleted;
      case 'auth_request':
        return Status.authRequest;
      case 'active':
        return Status.active;
      default:
        throw ArgumentError('Invalid raw value for Status: $rawValue');
    }
  }
}
