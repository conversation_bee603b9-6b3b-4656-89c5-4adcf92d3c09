part of 'delete_account_bloc.dart';

abstract class DeleteAccountEvent extends BaseBlocEvent {
  const DeleteAccountEvent();
}

class DeleteAccountRequestedEvent extends DeleteAccountEvent {
  final String username;

  const DeleteAccountRequestedEvent(this.username);

  DeleteAccountRequestedEvent copyWith({String? username}) {
    return DeleteAccountRequestedEvent(username ?? this.username);
  }
}

class ResetDeleteAccountStateEvent extends DeleteAccountEvent {
  const ResetDeleteAccountStateEvent();
}
