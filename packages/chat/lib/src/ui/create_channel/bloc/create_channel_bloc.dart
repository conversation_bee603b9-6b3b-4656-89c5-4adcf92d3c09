import 'dart:async';
import 'dart:typed_data';
import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart';
import 'package:cross_file/cross_file.dart';
import '../../../../chat.dart';
import '../../../domain/usecase/channel/create_channel_use_case.dart';

part 'create_channel_bloc.freezed.dart';

part 'create_channel_event.dart';

part 'create_channel_state.dart';

@injectable
class CreateChannelBloc
    extends BaseBloc<CreateChannelEvent, CreateChannelState> {
  CreateChannelBloc(
    this._createChannelUseCase,
    this._chatRepository,
    this._messageRepository,
    this._uploadImageHandler,
    this._updateChannelAvatarUseCase,
  ) : super(CreateChannelState.initial()) {
    on<InitiateCreateChannelEvent>(_onInit);
    on<CreateNewChannelEvent>(_onCreateChannel);
    on<ChangeAvatarChannelEvent>(_onChangeAvatar);
    on<ChangeNameChannelEvent>(_onChangeName);
    on<UpdateUserIDsInvitedUserEvent>(_onUpdateUserIDsInvitedUser);
    on<ChangeSearchingStatusEvent>(_onChangeSearchingStatus);
    on<ChangeSearchTextEvent>(_onChangeSearchText);
    on<UpdateChannelAvatarEvent>(_onUpdateAvatar);
  }

  final CreateChannelUseCase _createChannelUseCase;
  final ChannelRepository _chatRepository;
  final MessageRepository _messageRepository;

  final UploadImageHandler _uploadImageHandler;
  final UpdateChannelAvatarUseCase _updateChannelAvatarUseCase;

  FutureOr<void> _onInit(
    InitiateCreateChannelEvent event,
    Emitter<CreateChannelState> emit,
  ) {}

  Future<void> _onUpdateAvatar(
    UpdateChannelAvatarEvent event,
    Emitter<CreateChannelState> emit,
  ) async {
    UploadFile avatar = event.avatar;
    String workspaceId = event.workspaceId!;
    String channelId = event.channelId!;

    await _handleUpload(
      avatar,
      (String fileUrl) async {
        await _updateAvatarPath(workspaceId, channelId, fileUrl);
      },
    );
  }

  Future<void> _updateAvatarPath(
    String workspaceId,
    String channelId,
    String avatarPath,
  ) async {
    final UpdateChannelAvatarOutput output =
        await _updateChannelAvatarUseCase.execute(
      UpdateChannelAvatarInput(
        workspaceId: workspaceId,
        channelId: channelId,
        avatarPath: avatarPath,
      ),
    );

    if (output.success) {
      final avatarPath = output.avatarPath!;
      _chatRepository.getChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );
    } else {
      final error = output.error!;
      add(OnErrorEvent(errorMessage: error.message!));
    }
  }

  Future<void> _handleUpload(
    UploadFile file,
    Future<void> Function(String fileUrl) onSuccessHandler, {
    Function(String? messageError)? onMessageError,
  }) async {
    try {
      final result = await _uploadImageHandler.handleUpload(
        file: file,
        onSuccess: (UpFile file, String fileUrl) async {
          await onSuccessHandler(fileUrl);
        },
        onError: (UpFile file, ErrorCode errorCode, String message) {
          _error(file, errorCode, message);
          onMessageError?.call(message);
        },
      );

      if (!result.success) {
        add(
          OnErrorEvent(
            errorMessage: " Upload failed: ${result.errorMessage}",
          ),
        );
      }
    } catch (e) {
      add(OnErrorEvent(errorMessage: " Exception while uploading file: $e"));
    }
  }

  void _error(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        add(
          OnErrorEvent(
            errorMessage: "Update noInternet error! $message",
          ),
        );
        break;
      case ErrorCode.uploadError:
        add(OnErrorEvent(errorMessage: " Update error! $message"));
        break;
      default:
        add(OnErrorEvent(errorMessage: message));
    }
  }

  Future<void> _onCreateChannel(
    CreateNewChannelEvent event,
    Emitter<CreateChannelState> emit,
  ) async {
    Future<void> createChannel({String? avatarURL}) async {
      final output = await _createChannelUseCase.execute(
        CreateChannelInput(
          name: event.name,
          userIDsInvited: event.userIDsInvited,
          avatarURL: avatarURL,
        ),
      );

      if (output.channel != null) {
        final channel = output.channel!;
        final insertedId = _chatRepository.insert(channel);
        channel.id = insertedId;

        if (output.messages?.isNotEmpty ?? false) {
          _messageRepository.insertAll(output.messages!);
        }
        event.onCreated?.call(channel);
      } else {
        if (output.error != null) {
          event.onError?.call(Exception(output.error!));
        }
      }
    }

    try {
      if (event.avatarPath != null) {
        final avatar = await XFile(event.avatarPath ?? '');
        UploadFile avatarUploadFile = UploadFile(
          path: avatar.path,
          name: avatar.name,
          size: await avatar.length(),
          fileRef: RandomUtils.randomId(),
        );
        await _handleUpload(avatarUploadFile, (String fileUrl) async {
          createChannel(avatarURL: fileUrl);
        }, onMessageError: (String? messageError) {
          event.onError?.call(messageError);
        },);
      } else {
        createChannel();
      }
    } catch (ex) {
      event.onError?.call(ex);
    }
  }

  FutureOr<void> _onChangeAvatar(
    ChangeAvatarChannelEvent event,
    Emitter<CreateChannelState> emit,
  ) {
    emit(CreateChannelState.channelAvatarChanged(avatar: event.value));
  }

  FutureOr<void> _onChangeName(
    ChangeNameChannelEvent event,
    Emitter<CreateChannelState> emit,
  ) {
    emit(CreateChannelState.channelNameChanged(name: event.value));
  }

  FutureOr<void> _onUpdateUserIDsInvitedUser(
    UpdateUserIDsInvitedUserEvent event,
    Emitter<CreateChannelState> emit,
  ) {
    emit(
      CreateChannelState.userIDsInvitedUpdated(
        userIDsInvited: event.userIDsInvited,
      ),
    );
  }

  FutureOr<void> _onChangeSearchingStatus(
    ChangeSearchingStatusEvent event,
    Emitter<CreateChannelState> emit,
  ) {
    emit(
      CreateChannelState.searchingStatusChanged(
        isSearching: event.isSearching,
      ),
    );
  }

  FutureOr<void> _onChangeSearchText(
    ChangeSearchTextEvent event,
    Emitter<CreateChannelState> emit,
  ) {
    emit(CreateChannelState.searchTextChanged(keyword: event.keyword));
  }
}
