import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_receiver_widget.dart';

class InvitationWidgetImpl extends BaseReceiverWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final void Function(MessageItem messageItem)? onQuoteMessageClicked;
  final QuoteMessage? quoteMessage;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  InvitationWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.quoteMessage,
    this.onQuoteMessageClicked,
    this.isBotChannel = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    required super.messageItem,
    required super.message,
    super.key,
  });

  final bool isBotChannel;

  bool _isInvitationExpired() {
    final embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    final invitationData = embed?.invitationData;
    final past24Hour = invitationData?.updateTime == null
        ? false
        : (invitationData?.expireTime
                ?.toLocalDateTime()
                ?.isBefore(DateTime.now()) ??
            false);
    return (invitationData?.isExpired ?? false) || past24Hour;
  }

  @override
  Widget build(BuildContext context) {
    final embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    final invitationData = embed?.invitationData;

    final isExpired = _isInvitationExpired();

    final invitationMessage = InvitationMessage(
      channelName: invitationData?.channel?['name'] ?? '',
      numMembers: invitationData?.channel?['totalMembers'] ?? 0,
      avatarUrl: UrlUtils.parseAvatar(invitationData?.channel?['avatar']),
      qr: invitationData?.invitationLink ?? '',
      hasExpired: isExpired,
    );

    return InvitationMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      isShowAvatar: isShowAvatar,
      isShowCreateTime: isShowCreateTime,
      mentions: message.mentions ?? [],
      onQuote: onQuote,
      isHiddenPin: isHiddenPin,
      isShowName: isShowName,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onCopy: (messageItem) => onCopy(context, messageItem),
      emojiList: message.emojiList,
      onUsernameClicked: (username) => onClickMention(context, username),
      onLinkClicked: isExpired ? (_) {} : onClickLink,
      invitationMessage: invitationMessage,
      onOpenLink: (MessageItem messageItem) {},
      content: message.argsContent()!,
      quickReact: hasQuickReaction,
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      isBotChannel: isBotChannel,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: onQuoteMessageClicked,
      isHighlighted: isHighlighted,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onTranslateMessage: onTranslateMessage,
      shouldAnimate: shouldAnimate,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {
    if (!message.hasInvitationData || _isInvitationExpired()) return;

    final data = message.firstEmbed!.invitationData!;

    onClickLink(data.invitationLink!);
  }
}
