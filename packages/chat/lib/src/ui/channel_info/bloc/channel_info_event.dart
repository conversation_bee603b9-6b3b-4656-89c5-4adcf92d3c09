part of 'channel_info_bloc.dart';

sealed class ChannelInfoEvent extends BaseBlocEvent {
  const ChannelInfoEvent();
}

@freezed
sealed class InitiateChannelInfoEvent extends ChannelInfoEvent
    with _$InitiateChannelInfoEvent {
  const InitiateChannelInfoEvent._();
  factory InitiateChannelInfoEvent({
    Channel? channel,
    String? channelId,
    String? workspaceId,
  }) = _InitiateChannelInfoEvent;
}

@freezed
sealed class ChannelChangedEvent extends ChannelInfoEvent
    with _$ChannelChangedEvent {
  const ChannelChangedEvent._();
  factory ChannelChangedEvent({
    required Channel channel,
  }) = _ChannelChangedEvent;
}

@freezed
sealed class ListMembersChangedEvent extends ChannelInfoEvent
    with _$ListMembersChangedEvent {
  const ListMembersChangedEvent._();
  factory ListMembersChangedEvent({
    required List<Member> members,
  }) = _ListMembersChangedEvent;
}

@freezed
sealed class ListUsersChangedEvent extends ChannelInfoEvent
    with _$ListUsersChangedEvent {
  const ListUsersChangedEvent._();
  factory ListUsersChangedEvent({
    required List<ChatUser> users,
  }) = _ListUsersChangedEvent;
}

@freezed
sealed class ClearMessageAllForMeEvent extends ChannelInfoEvent
    with _$ClearMessageAllForMeEvent {
  const ClearMessageAllForMeEvent._();
  factory ClearMessageAllForMeEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = _ClearMessageAllForMeEvent;
}

@freezed
sealed class DeleteChannelAvatarEvent extends ChannelInfoEvent
    with _$DeleteChannelAvatarEvent {
  const DeleteChannelAvatarEvent._();
  factory DeleteChannelAvatarEvent({
    required String workspaceId,
    required String channelId,
  }) = _DeleteChannelAvatarEvent;
}

@freezed
sealed class UpdateChannelAvatarEvent extends ChannelInfoEvent
    with _$UpdateChannelAvatarEvent {
  const UpdateChannelAvatarEvent._();
  factory UpdateChannelAvatarEvent({
    required UploadFile avatar,
    String? workspaceId,
    String? channelId,
    String? userId,
    @Default(null) void Function(String)? onAvatarChanged,
  }) = _UpdateChannelAvatarEvent;
}

@freezed
sealed class OnErrorEvent extends ChannelInfoEvent with _$OnErrorEvent {
  const OnErrorEvent._();
  factory OnErrorEvent({
    required String errorMessage,
  }) = _OnErrorEvent;
}

class OnAvatarUpdatedEvent extends ChannelInfoEvent {
  const OnAvatarUpdatedEvent(this.avatarPath);

  final String? avatarPath;
}

@freezed
sealed class OnRefreshEvent extends ChannelInfoEvent with _$OnRefreshEvent {
  const OnRefreshEvent._();
  factory OnRefreshEvent() = _OnRefreshEvent;
}
