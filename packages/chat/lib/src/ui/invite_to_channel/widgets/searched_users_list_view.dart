import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../data/models/invitable_user.dart';
import '../bloc/search_users/search_users_bloc.dart';
import 'list_skeletons.dart';

class SearchedUsersListView extends StatefulWidget {
  const SearchedUsersListView({
    required this.keyword,
    required this.onTapUser,
    this.selectedUserIds,
    this.memberIds,
    this.listPadding,
    super.key,
  });

  final String keyword;
  final void Function(InvitableUser item) onTapUser;
  final List<String>? selectedUserIds;
  final List<String>? memberIds;
  final EdgeInsets? listPadding;

  @override
  State<SearchedUsersListView> createState() => SearchedUsersListViewState();
}

const _firstPageKey = '0';

class SearchedUsersListViewState extends State<SearchedUsersListView> {
  PagingController<String, InvitableUser> _pagingController =
      PagingController(firstPageKey: _firstPageKey);
  String _nextPageToken = '1';
  String _keyword = '';
  bool _isWidgetUpdated = false;

  List<String> get selectedUserIds => widget.selectedUserIds ?? [];

  List<String> get memberIds => widget.memberIds ?? [];
  late final SearchUsersBloc _searchUsersBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  List<UserPrivateData> _listUserPrivateData = [];

  @override
  void initState() {
    super.initState();
    _searchUsersBloc = getIt<SearchUsersBloc>();
    _searchUsersBloc.add(InitiateSearchUsersEvent());
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _keyword = widget.keyword;
    if (_keyword.isNotEmpty) {
      _getData();
    }
    _pagingController.addPageRequestListener(
      (pageKey) {
        if (pageKey != _firstPageKey) {
          _getData();
        }
      },
    );
  }

  Future<void> onRefresh(String keyword) async {
    _keyword = keyword;
    _nextPageToken = '1';
    _pagingController.refresh();
    _getData();
  }

  void _getData() {
    _searchUsersBloc.add(
      SearchingUsersEvent(
        keyword: _keyword,
        nextPageToken: _nextPageToken,
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant SearchedUsersListView oldWidget) {
    if (oldWidget.keyword != widget.keyword) {
      _isWidgetUpdated = true;
      onRefresh(widget.keyword);
    }
    super.didUpdateWidget(oldWidget);
  }

  void _blocListener(BuildContext context, SearchUsersState state) {
    state.when(
      initial: () {},
      searching: () {},
      searchedUsers: (users, nextPageToken, hasNext) {
        _nextPageToken = nextPageToken;
        if (hasNext) {
          _pagingController.appendPage(users, _nextPageToken);
        } else {
          _pagingController.appendLastPage(users);
        }
      },
    );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        _pagingController.itemList?.forEach((item) {
          var listUserPrivateDataFiltered = listUserPrivateData
              .firstWhere((user) => user.userId == item.userId);
          item.aliasName = listUserPrivateDataFiltered.aliasName;
        });
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SearchUsersBloc>.value(value: _searchUsersBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<SearchUsersBloc, SearchUsersState>(
          buildWhen: (prev, current) {
            if (!prev.isSameFactory(current)) {
              return true;
            }
            return prev.isStateSearched &&
                current.isStateSearched &&
                prev.invitableUsers != current.invitableUsers;
          },
          builder: (context, state) {
            Widget child = SizedBox.shrink();
            if (_isWidgetUpdated) {
              _isWidgetUpdated = false;
              child = _keyword.isNotEmpty
                  ? ListSkeletons()
                  : ui.NoInvitableUsersWidget();
            } else {
              state.when(
                initial: () {
                  child = ui.NoInvitableUsersWidget();
                },
                searching: () {
                  child = ListSkeletons();
                },
                searchedUsers: (users, nextPageToken, hasNext) {
                  if (users.isEmpty) {
                    child = ui.SearchNoResultsFoundWidget();
                  } else {
                    if (hasNext && _nextPageToken != nextPageToken) {
                      _nextPageToken = nextPageToken;
                      _pagingController.appendPage(users, _nextPageToken);
                    } else {
                      if (_pagingController.nextPageKey != null) {
                        _pagingController.appendLastPage(users);
                      }
                    }
                    child = BlocListener<SearchUsersBloc, SearchUsersState>(
                      listener: _blocListener,
                      child: PagedListView<String, InvitableUser>(
                        padding: widget.listPadding,
                        keyboardDismissBehavior:
                            ScrollViewKeyboardDismissBehavior.onDrag,
                        pagingController: _pagingController,
                        builderDelegate:
                            PagedChildBuilderDelegate<InvitableUser>(
                          itemBuilder: (context, item, index) {
                            final account = ui.ItemAccountInvitation(
                              name: getAliasName(item.userId) ?? item.name,
                              id: item.userId,
                              url: UrlUtils.parseAvatar(item.avatar),
                            );
                            final isSelected =
                                selectedUserIds.contains(item.userId) ||
                                    memberIds.contains(item.userId);
                            return ui.AccountWithCheckbox(
                              onTap: () => widget.onTapUser(item),
                              account: account,
                              isSelected: isSelected,
                            );
                          },
                          noItemsFoundIndicatorBuilder: (_) {
                            return Container();
                          },
                          newPageProgressIndicatorBuilder: (_) {
                            return Center(
                              child: ui.AppCircularProgressIndicator(),
                            );
                          },
                          firstPageProgressIndicatorBuilder: (_) {
                            return Container();
                          },
                        ),
                      ),
                    );
                  }
                },
              );
            }
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 250),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(opacity: animation, child: child);
              },
              child: child,
            );
          },
        ),
      ),
    );
  }
}
