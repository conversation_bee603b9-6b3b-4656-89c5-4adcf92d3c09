part of 'forward_bloc.dart';

sealed class ForwardEvent extends BaseBlocEvent {
  const ForwardEvent();
}

@freezed
sealed class OnInitForwardEvent extends ForwardEvent with _$OnInitForwardEvent {
  const OnInitForwardEvent._();
  factory OnInitForwardEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = _OnInitForwardEvent;
}

class OnSearchEvent extends ForwardEvent {
  const OnSearchEvent({this.keyword});

  final String? keyword;
}

class OnShareEvent extends ForwardEvent {
  const OnShareEvent({required this.selectedAccounts});

  final List<UserItem> selectedAccounts;
}

class OnWaitingEvent extends ForwardEvent {
  const OnWaitingEvent({required this.listForward});

  final List<UserItem> listForward;
}

class OnChannelLoadedEvent extends ForwardEvent {
  const OnChannelLoadedEvent({required this.listForward});

  final List<UserItem> listForward;
}

class OnLoadSearchEvent extends ForwardEvent {
  const OnLoadSearchEvent({required this.search});

  final List<UserItem> search;
}
