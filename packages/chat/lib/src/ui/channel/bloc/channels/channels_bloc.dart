import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/translate_to_repository.dart';
import '../../../../domain/usecase/channel/sync_channels_use_case.dart';
import '../../../../domain/usecase/meeting_room/get_meeting_room_list_use_case.dart';

part 'channels_bloc.freezed.dart';
part 'channels_event.dart';
part 'channels_state.dart';

@injectable
class ChannelsBloc extends BaseBloc<ChannelsEvent, ChannelsState> {
  ChannelsBloc(
    this._loadListChannelsUseCase,
    this._syncChannelsUseCase,
    this._deleteChannelUseCase,
    this._channelRepository,
    this._messageRepository,
    this._translateToRepository,
    this._clearMessageAllForMeUseCase,
    this._clearMessageAllForEveryoneUseCase,
    this._managerRepository,
    this._chatUserRepository,
    this._deleteAllMessagesUseCase,
    this._getMeetingRoomListUseCase,
  ) : super(ChannelsState.initial()) {
    on<InitiateChannelsEvent>(_onInit);
    on<LoadMoreChannelsEvent>(_onLoadMoreChannels);
    on<UpdateTranslatedMessagesEvent>(_onUpdateTranslatedMessages);
    on<SyncChannelsEvent>(_onSyncChannels);
    on<UpdateOrAddChannelsEvent>(_onUpdateOrAddChannels);
    on<DeleteChannelsEvent>(_onDeleteChannel);
    on<RemoveChannelEvent>(_onRemoveChannel);
    on<UpdateChannelsListEvent>(_onUpdateChannelsList);
    on<CallClearMessageAllForMeChannelEvent>(_onCallClearMessageAllForMe);
    on<ClearMessageAllForMeChannelEvent>(_onClearMessageAllForMe);
    on<CallClearMessageAllForEveryoneChannelEvent>(
      _onCallClearMessageAllForEveryOne,
    );
    on<ClearMessageAllForEveryoneChannelEvent>(_onClearMessageAllForEveryone);
    on<RefreshClearMessageEvent>(_onRefreshClearMessage);
    on<UpdateMeetingRoomListEvent>(_onUpdateMeetingRoomList);
  }

  final LoadListChannelsUseCase _loadListChannelsUseCase;
  final SyncChannelsUseCase _syncChannelsUseCase;
  final DeleteChannelUseCase _deleteChannelUseCase;
  final ClearMessageAllForMeUseCase _clearMessageAllForMeUseCase;
  final ClearMessageAllForEveryoneUseCase _clearMessageAllForEveryoneUseCase;
  final DeleteAllMessagesUseCase _deleteAllMessagesUseCase;
  final GetMeetingRoomListUseCase _getMeetingRoomListUseCase;

  final ChannelRepository _channelRepository;
  final MessageRepository _messageRepository;
  final ManagerRepository _managerRepository;
  final ChatUserRepository _chatUserRepository;
  final TranslateToRepository _translateToRepository;
  Set<String> _lastMessageId = Set.of([]);

  late bool _noMoreItems;

  static const _channelRemotePageSize = 500;

  StreamSubscription? _channelsSubscription;
  StreamSubscription? _translateSubscription;

  Map<String, String> _cacheMessageIdUserId = {};
  Map<String, String> _cacheUserIdUsername = {};

  @override
  Future<void> close() {
    _channelsSubscription?.cancel();
    _translateSubscription?.cancel();
    return super.close();
  }

  Future<void> _onInit(
    InitiateChannelsEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    _noMoreItems = _managerRepository.getLoadedAllChannelsStatus();
    _channelsSubscription?.cancel();
    _channelsSubscription =
        _channelRepository.observerListChannels(_observerChannels);

    _getMeetingRoomListUseCase
        .execute(GetMeetingRoomListInput())
        .then((output) {
      add(
        UpdateMeetingRoomListEvent(mapMeetingChannelIds: output.mapChannelIds),
      );
    }).onError((error, stackTrace) {
      Log.e(name: 'GetMeetingRoomList', error);
    });
  }

  //region Channels list
  void _observerChannels(List<Channel> channels) {
    _observeTranslatedMessages(
      channels
          .where((channel) => channel.lastMessageId != null)
          .map((channel) => channel.lastMessageId!)
          .toSet(),
    );
    add(UpdateChannelsListEvent(channels: channels, noMoreItems: _noMoreItems));
  }

  Future<void> _onLoadMoreChannels(
    LoadMoreChannelsEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    emit(ChannelsState.loading());
    final remoteChannelsOutput = await _loadListChannelsUseCase.execute(
      LoadListChannelsInput(
        limit: _channelRemotePageSize,
        nextPageToken: event.nextPageToken.isEmpty ? null : event.nextPageToken,
      ),
    );
    _noMoreItems = !remoteChannelsOutput.hasNext;
    if (_noMoreItems) {
      _managerRepository.updateLoadedAllChannelsStatus(true);
    }
    if (remoteChannelsOutput.channels.isEmpty) {
      add(UpdateChannelsListEvent(channels: [], noMoreItems: _noMoreItems));
      return;
    }
    _channelRepository.insertAll(remoteChannelsOutput.channels);
  }

  Future<void> _onSyncChannels(
    SyncChannelsEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    final lastTime = _messageRepository.getLastMessageCreated();
    if (lastTime != null) {
      await _syncChannelsUseCase.execute(
        SyncChannelsInput(updateTimeAfter: lastTime),
      );
    }
  }

  FutureOr<void> _onUpdateOrAddChannels(
    UpdateOrAddChannelsEvent event,
    Emitter<ChannelsState> emit,
  ) {
    emit(ChannelsState.channelCreatedOrUpdated(channel: event.channel));
  }

  Future<void> _onDeleteChannel(
    DeleteChannelsEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    final output = await _deleteChannelUseCase.execute(
      DeleteChannelInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
    if (output.ok) {
      event.onDeleted?.call();

      // Notify the end meeting room event
      AppEventBus.publish(
        EndMeetingRoomEvent(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
        ),
      );

      emit(
        ChannelsState.channelDeleted(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
        ),
      );
    }
  }

  FutureOr<void> _onRemoveChannel(
    RemoveChannelEvent event,
    Emitter<ChannelsState> emit,
  ) {
    emit(
      ChannelsState.channelDeleted(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }

  FutureOr<void> _onUpdateChannelsList(
    UpdateChannelsListEvent event,
    Emitter<ChannelsState> emit,
  ) {
    emit(
      ChannelsState.loaded(
        channels: event.channels,
        noMoreItems: event.noMoreItems,
      ),
    );
  }

  FutureOr<void> _onUpdateMeetingRoomList(
    UpdateMeetingRoomListEvent event,
    Emitter<ChannelsState> emit,
  ) {
    emit(
      ChannelsState.loadedMeetingRoomList(
        mapMeetingChannelIds: event.mapMeetingChannelIds,
      ),
    );
  }

  //endregion Channels list

  //region Clear messages
  FutureOr<void> _onCallClearMessageAllForMe(
    CallClearMessageAllForMeChannelEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    emit(
      ChannelsState.callClearMessageAllForMe(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
  }

  FutureOr<void> _onClearMessageAllForMe(
    ClearMessageAllForMeChannelEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    ClearMessageAllForMeOutput clearMessageAllForMeOutputOutput =
        await _clearMessageAllForMeUseCase.execute(
      ClearMessageAllForMeInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (clearMessageAllForMeOutputOutput.ok == true) {
      if (event.channelId != null) {
        AppEventBus.publish(UnPinChannelEvent(channelId: event.channelId!));
      }
      await _deleteAllMessagesUseCase.execute(
        DeleteAllMessagesInput(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
        ),
      );

      emit(
        ChannelsState.clearMessageAllForMe(
          response: clearMessageAllForMeOutputOutput.ok,
        ),
      );
      return;
    }
    emit(ChannelsState.clearMessageAllForMe(response: false));
  }

  FutureOr<void> _onCallClearMessageAllForEveryOne(
    CallClearMessageAllForEveryoneChannelEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    emit(
      ChannelsState.callClearMessageAllForEveryone(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
  }

  FutureOr<void> _onClearMessageAllForEveryone(
    ClearMessageAllForEveryoneChannelEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    ClearMessageAllForEveryoneOutput clearMessageAllForEveryoneOutput =
        await _clearMessageAllForEveryoneUseCase.execute(
      ClearMessageAllForEveryoneInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (clearMessageAllForEveryoneOutput.ok == true) {
      if (event.channelId != null) {
        AppEventBus.publish(UnPinChannelEvent(channelId: event.channelId!));
      }
      await _deleteAllMessagesUseCase.execute(
        DeleteAllMessagesInput(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
        ),
      );
      emit(
        ChannelsState.clearMessageAllForEveryone(
          response: clearMessageAllForEveryoneOutput.ok,
        ),
      );
      return;
    }

    emit(ChannelsState.clearMessageAllForEveryone(response: false));
  }

  FutureOr<void> _onRefreshClearMessage(
    RefreshClearMessageEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    emit(ChannelsState.refreshClearMessage());
  }

  //endregion Clear messages

  //region Translate message
  void _observeTranslatedMessages(Set<String> messageIds) {
    if (messageIds == _lastMessageId) return;
    _lastMessageId = messageIds;
    _translateSubscription?.cancel();
    if (messageIds.isEmpty) return;
    _translateSubscription =
        _translateToRepository.observerTranslatedResultsByMessageIds(
      messageIds,
      Config.getInstance().activeSessionKey!,
      (results) {
        add(
          UpdateTranslatedMessagesEvent(
            translatedMessages: {
              for (final result in results)
                result.messageId: result.translatedContent,
            },
          ),
        );
      },
    );
  }

  Future<void> _onUpdateTranslatedMessages(
    UpdateTranslatedMessagesEvent event,
    Emitter<ChannelsState> emit,
  ) async {
    emit(
      ChannelsState.translatedMessages(
        translatedMessages: event.translatedMessages,
      ),
    );
  }

  //endregion Translate message

  //region Public methods
  ChatUser? getRecipientUser(String recipientId) {
    return _chatUserRepository.getUser(recipientId);
  }

  /// Return the username if the message is a user message, return null otherwise
  String? getUserNameOfUserSendMessage({
    required String workspaceId,
    required String channelId,
    String? messageId,
  }) {
    if (messageId == null) {
      return null;
    }
    final userId = _cacheMessageIdUserId[messageId];
    if (userId != null) {
      if (_cacheUserIdUsername[userId] != null) {
        return _cacheUserIdUsername[userId]!;
      }
    }

    final message = _messageRepository.getMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );
    if (message == null) {
      return null;
    }
    _cacheMessageIdUserId[messageId] = message.userId;

    // system messages
    if (message.messageViewTypeRaw <= 2) {
      return null;
    }

    if (_cacheUserIdUsername[message.userId] != null) {
      return _cacheUserIdUsername[message.userId]!;
    }

    // User messages
    final user = _chatUserRepository.getUser(message.userId);
    if (user == null) {
      return null;
    }
    _cacheUserIdUsername[user.userId] = user.username!;
    return "${user.username}";
  }
//endregion Public methods
}
