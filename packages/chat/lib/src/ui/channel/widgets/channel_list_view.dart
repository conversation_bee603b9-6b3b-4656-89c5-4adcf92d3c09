part of '../channels_page.dart';

class ChannelListView extends StatefulWidget {
  const ChannelListView({super.key, this.onTapChannel});

  final void Function(
    String workspaceId,
    String channelId,
    String? userId,
  )? onTapChannel;

  @override
  State<ChannelListView> createState() => ChannelListViewState();
}

const _firstPageToken = '';

class ChannelListViewState extends State<ChannelListView> {
  final PagingController<String, Channel> _pagingController =
      PagingController(firstPageKey: _firstPageToken);

  var _nextPageToken = _firstPageToken;
  final ValueNotifier<bool> _noMoreItemsNotifier = ValueNotifier(true);
  final _channelEventsHandler = ChannelEventsHandler();

  ValueNotifier<String> _selectedItemNotifier = ValueNotifier('');
  ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);
  ValueNotifier<String> processContent = ValueNotifier("");
  late AppLocalizations appLocalizations;
  Map<String, UserPrivateData> _mapUserPrivateData = {};
  Map<String, ChannelPrivateData> _mapChannelPrivateData = {};
  Map<String, ChatUser> _mapBlockUser = {};
  late ValueNotifier<bool> _isBlocUserReport = ValueNotifier(false);
  late bool is24HourFormat;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  Map<String, String?> _translatedMessages = {};

  StreamSubscription? _callGroupEventSubscription;
  Map<String, bool> _mapMeetingChannelIds = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _channelEventsHandler.channelsBloc = context.read<ChannelsBloc>();
    });
    _pagingController.addPageRequestListener(
      (pageKey) {
        if (!_noMoreItemsNotifier.value) {
          context.read<ChannelsBloc>().add(
                LoadMoreChannelsEvent(
                  nextPageToken: _nextPageToken,
                ),
              );
        }
      },
    );
    is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;

    //Listen meeting/call events
    _callGroupEventSubscription = getIt<AppEventBus>()
        .on<CallGroupLocalEvent>()
        .listen(_handleCallGroupEvent);
  }

  /// Handle Show/Hide minimize call group widget
  void _handleCallGroupEvent(CallGroupLocalEvent event) {
    String key = '${event.channelId}';
    if (event is ShowMinimizedCallGroupEvent) {
      setState(() {
        _mapMeetingChannelIds[key] = true;
      });
      return;
    }
    if (event is HideMinimizedCallGroupEvent) {
      setState(() {
        _mapMeetingChannelIds.remove(key);
      });
      return;
    }
  }

  void popToHome() {
    AppEventBus.publish(
      PopToHomeEvent(),
    );
  }

  void changeProcessDialog(ui.ProcessStatus status, String content) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(Duration(seconds: 2), () {
        popToHome();
      });
    });
  }

  @override
  void dispose() {
    _pagingController.dispose();
    _channelEventsHandler.dispose();
    _callGroupEventSubscription?.cancel();
    super.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
  }

  Future<void> onRefresh() async {
    context.read<ChannelsBloc>().add(
          LoadMoreChannelsEvent(
            nextPageToken: _nextPageToken,
          ),
        );
  }

  void addItemToTop(Channel item) {
    _pagingController.itemList = [
      item,
      ..._pagingController.itemList ?? [],
    ];
  }

  void _handleLoaded(
    List<Channel> channels,
    bool noMoreItems,
  ) {
    if (!noMoreItems && channels.isEmpty) {
      _noMoreItemsNotifier.value = noMoreItems;
      context.read<ChannelsBloc>().add(
            LoadMoreChannelsEvent(
              nextPageToken: _nextPageToken,
            ),
          );
      return;
    }
    if (noMoreItems) {
      _noMoreItemsNotifier.value = noMoreItems;
      _pagingController.appendLastPage([]);
    }
    if (channels.isEmpty) {
      _pagingController.itemList = [];
      return;
    }
    _nextPageToken = channels.last.channelId;
    List<Channel> sortChannels = sortChannelByPin(channels: channels);
    _pagingController.itemList = sortChannels;
    _noMoreItemsNotifier.value = noMoreItems;
  }

  void _handleChannelCreatedOrUpdated(Channel channel) {
    final newItemList = List<Channel>.from(_pagingController.itemList ?? []);
    final oldItemIndex = newItemList.indexWhere(
      (item) =>
          item.channelId == channel.channelId &&
          item.workspaceId == channel.workspaceId,
    );
    if (oldItemIndex != -1) {
      newItemList.removeAt(oldItemIndex);
    }
    newItemList.insert(0, channel);
    _pagingController.itemList = [...newItemList];
  }

  void _handleChannelDeleted(String workspaceId, String channelId) {
    final newItemList = List<Channel>.from(_pagingController.itemList ?? []);
    final oldItemIndex = newItemList.indexWhere(
      (item) => item.channelId == channelId && item.workspaceId == workspaceId,
    );
    if (oldItemIndex != -1) {
      newItemList.removeAt(oldItemIndex);
      _pagingController.itemList = newItemList;
    }
  }

  void _blocChannelListener(BuildContext context, ChannelsState state) {
    state.when(
      initial: () {},
      loading: () {},
      loaded: _handleLoaded,
      channelCreatedOrUpdated: _handleChannelCreatedOrUpdated,
      channelDeleted: _handleChannelDeleted,
      callClearMessageAllForMe: (workspaceId, channelId, userId) {
        if (userId != null) {
          processContent.value = appLocalizations.clearingMessages;
          processStatus.value = ui.ProcessStatus.loading;
          ui.DialogUtils.showProcessStatusDialog(
            context,
            processStatus: processStatus,
            processContent: processContent,
          );
          context.read<ChannelsBloc>().add(
                ClearMessageAllForMeChannelEvent(
                  workspaceId: workspaceId,
                  channelId: channelId,
                  userId: userId,
                ),
              );
        }
      },
      callClearMessageAllForEveryone: (workspaceId, channelId, userId) {
        if (channelId != null) {
          processContent.value = appLocalizations.clearingMessages;
          processStatus.value = ui.ProcessStatus.loading;
          ui.DialogUtils.showProcessStatusDialog(
            context,
            processStatus: processStatus,
            processContent: processContent,
          );
          context.read<ChannelsBloc>().add(
                ClearMessageAllForEveryoneChannelEvent(
                  workspaceId: workspaceId,
                  channelId: channelId,
                  userId: userId,
                ),
              );
        }
      },
      clearMessageAllForMe: (response) {
        if (response) {
          changeProcessDialog(
            ui.ProcessStatus.success,
            appLocalizations.messagesRemoved,
          );
        } else {
          changeProcessDialog(
            ui.ProcessStatus.failed,
            appLocalizations.clearingProcessFailed,
          );
        }
      },
      clearMessageAllForEveryone: (response) {
        if (response) {
          changeProcessDialog(
            ui.ProcessStatus.success,
            appLocalizations.messagesRemoved,
          );
        } else {
          changeProcessDialog(
            ui.ProcessStatus.failed,
            appLocalizations.clearingProcessFailed,
          );
        }
      },
      refreshClearMessage: () {},
      translatedMessages: (Map<String, String?> translatedMessages) {
        setState(() {
          _translatedMessages = translatedMessages;
        });
      },
      loadedMeetingRoomList: (Map<String, bool> mapMeetingChannelIds) {
        setState(() {
          _mapMeetingChannelIds = Map.from(mapMeetingChannelIds);
        });
      },
    );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _mapUserPrivateData = {};
        listUserPrivateData.forEach((item) {
          _mapUserPrivateData[item.userId] = item;
        });
        List<Channel> channels =
            List<Channel>.of(_pagingController.itemList ?? []);
        channels.forEach((item) {
          try {
            if (item.type?.isDm == true) {
              item.name = getAliasName(item.recipientId) ?? item.name;
            }
          } catch (error) {}
        });
        _pagingController.itemList = channels;
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      var user = _mapUserPrivateData[userId];
      return user?.aliasName != null && user!.aliasName!.isNotEmpty
          ? user.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void _blocChannelPrivateListener(
    BuildContext context,
    ChannelPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listChannelPrivateData:
          (List<ChannelPrivateData> listChannelPrivateData) {
        _mapChannelPrivateData = {};
        listChannelPrivateData.forEach((item) {
          _mapChannelPrivateData.putIfAbsent(item.channelId, () => item);
        });
        _pagingController.itemList = sortChannelByPin();
      },
    );
  }

  List<Channel> sortChannelByPin({List<Channel>? channels}) {
    List<Channel> _channels =
        List<Channel>.of(channels ?? _pagingController.itemList ?? []);
    _channels.sort((a, b) {
      int firstPriority = _mapChannelPrivateData[a.channelId]?.sort ??
          -1; // value default when not found
      int lastPriority = _mapChannelPrivateData[b.channelId]?.sort ?? -1;
      if (lastPriority != firstPriority) {
        return lastPriority.compareTo(firstPriority);
      }

      // sort desc lastMessage
      final firstTime = a.lastMessageCreateTime ?? '';
      final lastTime = b.lastMessageCreateTime ?? '';
      return lastTime.compareTo(firstTime);
    });
    return _channels;
  }

  void _blocUserReportListener(
    BuildContext context,
    UserReportState state,
  ) {
    state.maybeWhen(
      initial: () {},
      showProcessDialog: () {},
      updateProcessDialog: (response, userId, name, isBlocked) {
        if (response) {
          showReportThankYou(userId, name!, isBlocked);
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popToHome();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void showReportThankYou(String? userId, String name, bool? isBlocked) {
    _isBlocUserReport.value = isBlocked != null
        ? isBlocked
        : _mapBlockUser[userId] == null
            ? false
            : true;
    ui.BottomSheetUtil.showThankYouDmChannelBottomSheet(
      context: context,
      enableDrag: false,
      isDismissible: false,
      onClickClose: () {
        popToHome();
      },
      onClickBlock: () {
        ui.ActionSheetUtil.showBlockUserActionSheet(
          context,
          username: name,
          onBlock: () {
            context
                .read<BlockUserBloc>()
                .add(OnBlockUserEvent(userId: userId!, popOnlyMine: true));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickCommunityStandard: () async {
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathCommunityStandards,
        );
      },
      blockedUsername: name,
      isBlocked: _isBlocUserReport,
    );
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};

        listBlockUser?.forEach((item) {
          _mapBlockUser.putIfAbsent(
            item.userId,
            () => ChatUser.fromJson(item.toJson()),
          );
        });
      },
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          _isBlocUserReport.value = true;
          if (Navigator.canPop(context)) {
            popOnlyMine == true ? Navigator.pop(context) : popToHome();
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void _blocSettingNotification(
    BuildContext context,
    SettingNotificationState state,
  ) {
    state.maybeWhen(
      subscribeChannel: (response) {
        if (response == true) {
          popToHome();
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      unsubscribeChannel: (response) {
        if (response == true) {
          popToHome();
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void _appBlocListener(
    BuildContext context,
    AppState state,
  ) {
    if (is24HourFormat != state.is24HourFormat) {
      setState(() {
        is24HourFormat = state.is24HourFormat;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    appLocalizations = AppLocalizations.of(context)!;
    return MultiBlocListener(
      listeners: [
        BlocListener<ChannelsBloc, ChannelsState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocChannelListener,
        ),
        BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserPrivateListener,
        ),
        BlocListener<ChannelPrivateDataBloc, ChannelPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocChannelPrivateListener,
        ),
        BlocListener<BlockUserBloc, BlockUserState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocBlockUserListener,
        ),
        BlocListener<UserReportBloc, UserReportState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserReportListener,
        ),
        BlocListener<SettingNotificationBloc, SettingNotificationState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocSettingNotification,
        ),
        BlocListener<AppBloc, AppState>(
          listenWhen: (prev, state) => prev != state,
          listener: _appBlocListener,
        ),
      ],
      child: PagedSliverList<String, Channel>(
        pagingController: _pagingController,
        builderDelegate: PagedChildBuilderDelegate<Channel>(
          animateTransitions: true,
          itemBuilder: (context, channel, index) {
            return ValueListenableBuilder(
              valueListenable: _selectedItemNotifier,
              builder: (context, selectItem, _) {
                return ChannelItem(
                  channel: channel,
                  onTap: widget.onTapChannel,
                  channelSelectedId: selectItem,
                  isInCall: _mapMeetingChannelIds[channel.channelId] ?? false,
                  is24HourFormat: is24HourFormat,
                  onSelectChannel: (String value) {
                    _selectedItemNotifier.value = value;
                  },
                  getAliasName: getAliasName,
                  isPinChannel:
                      _mapChannelPrivateData[channel.channelId]?.pinned ??
                          false,
                  getTranslatedMessageContent: (String? messageId) {
                    if (StringUtils.isNullOrEmpty(messageId)) return null;
                    return _translatedMessages[messageId];
                  },
                );
              },
            );
          },
          firstPageProgressIndicatorBuilder: (_) {
            return ChannelListSkeleton();
          },
          noItemsFoundIndicatorBuilder: (_) {
            return ValueListenableBuilder(
              valueListenable: _noMoreItemsNotifier,
              builder: (context, noMoreItems, _) {
                if (noMoreItems) {
                  return ui.EmptyChannelWidget(
                    onAddContactClicked: () {},
                  );
                }
                return ChannelListSkeleton();
              },
            );
          },
          newPageProgressIndicatorBuilder: (_) {
            return Center(child: ui.AppCircularProgressIndicator());
          },
        ),
      ),
    );
  }
}
