import 'dart:typed_data';

import 'package:livekit_client/livekit_client.dart';

abstract class ChannelsInterface {
  void onTapMessageRequest();

  void onTapScanQR();

  void onTapTranslate();

  void onTapChannel(
    String workspaceId,
    String channelId,
    String? userId,
  );

  void opTapTakePhotoAvatar();

  void onTapOpenGalleryAvatar();

  void openImageViewPage(Uint8List avatarData);

  void onTapFriendStatus(
    String userId,
  );

  void goToMeetingRoom({
    required String channelId,
    required String workspaceId,
    required String channelName,
    required Room room,
    required bool isVideoCall,
  });
}
