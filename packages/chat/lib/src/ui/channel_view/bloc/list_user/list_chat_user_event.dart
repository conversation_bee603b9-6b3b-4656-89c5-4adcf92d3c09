part of 'list_chat_user_bloc.dart';

sealed class ListChatUserEvent {
  const ListChatUserEvent();
}

@freezed
sealed class ListChatUserInit extends ListChatUserEvent
    with _$ListChatUserInit {
  const ListChatUserInit._();
  factory ListChatUserInit({
    required Set<String> setIds,
  }) = _ListChatUserInit;
}

@freezed
sealed class ListChatUserOnChanged extends ListChatUserEvent
    with _$ListChatUserOnChanged {
  const ListChatUserOnChanged._();
  factory ListChatUserOnChanged(List<ChatUser> users) = _ListChatUserOnChanged;
}

@freezed
sealed class ListChatUserUnSubscription extends ListChatUserEvent
    with _$ListChatUserUnSubscription {
  const ListChatUserUnSubscription._();
  factory ListChatUserUnSubscription() = _ListChatUserUnSubscription;
}
