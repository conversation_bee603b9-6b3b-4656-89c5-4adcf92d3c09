import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_video_player/ziichat_video_player.dart';

class OriginalVideoActionSheetWidget extends StatefulWidget {
  const OriginalVideoActionSheetWidget({
    super.key,
    required this.videoPath,
    required this.messageItem,
  });

  final String videoPath;
  final ui.MessageItem messageItem;

  @override
  State<OriginalVideoActionSheetWidget> createState() =>
      _OriginalVideoActionSheetWidgetState();
}

class _OriginalVideoActionSheetWidgetState
    extends State<OriginalVideoActionSheetWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      height: 250.h,
      child: ActionSheetVideoPlayerWidget(
        videoPath: widget.videoPath,
        playButton: _buildPlayButton(),
      ),
    );
  }

  Widget _buildPlayButton() {
    return SizedBox(
      width: 60.w,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(0, 0, 0, 0.3),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: ui.AppAssets.pngIconAsset(
            ui.AppAssets.icPlayNew,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
