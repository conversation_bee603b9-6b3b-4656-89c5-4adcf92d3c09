import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:member_api/member_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class AssignAsAdminUseCase
    extends BaseFutureUseCase<AssignAsAdminInput, AssignAsAdminOutput> {
  const AssignAsAdminUseCase(
    this._memberRepository,
  );

  final MemberRepository _memberRepository;

  @override
  Future<AssignAsAdminOutput> buildUseCase(
    AssignAsAdminInput input,
  ) async {
    try {
      final bodyBuilder = V3AssignAsAdminRequestBuilder();
      bodyBuilder
        ..userId = input.userId
        ..workspaceId = input.workspaceId
        ..channelId = input.channelId;
      final response = await MemberClient()
          .instance
          .assignAsAdmin(body: bodyBuilder.build());
      if (response.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Member.serializer,
            response.data!.data!.member,
          ),
        );
        final member = MemberSerializer.serializeFromJson(data: json);
        if (member != null) {
          _memberRepository.forceInsert(member);
        }
        return AssignAsAdminOutput(member: member);
      }
    } catch (ex) {
      Log.e(ex);
    }
    AppEventBus.publish(DialogErrorOccurredEvent());
    return AssignAsAdminOutput();
  }
}

class AssignAsAdminInput extends BaseInput {
  const AssignAsAdminInput({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
  });

  final String userId;
  final String channelId;
  final String workspaceId;
}

class AssignAsAdminOutput extends BaseOutput {
  const AssignAsAdminOutput({
    this.member,
  });

  final Member? member;
}
