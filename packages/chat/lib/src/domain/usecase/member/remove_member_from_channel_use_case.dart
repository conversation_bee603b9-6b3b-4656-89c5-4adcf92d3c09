import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class RemoveMemberFromChannelUseCase extends BaseFutureUseCase<
    RemoveMemberFromChannelInput, RemoveMemberFromChannelOutput> {
  const RemoveMemberFromChannelUseCase(
    this._memberRepository,
  );

  final MemberRepository _memberRepository;

  @override
  Future<RemoveMemberFromChannelOutput> buildUseCase(
    RemoveMemberFromChannelInput input,
  ) async {
    try {
      final response = await MemberClient().instance.removeFromChannel(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
            userId: input.userId,
          );
      final ok = response.data?.ok ?? false;
      if (ok) {
        _memberRepository.revokeAllMemberRole(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
          userId: input.userId,
        );
        return RemoveMemberFromChannelOutput(ok: ok);
      }
    } catch (ex) {
      Log.e(ex);
    }
    AppEventBus.publish(DialogErrorOccurredEvent());
    return RemoveMemberFromChannelOutput(ok: false);
  }
}

class RemoveMemberFromChannelInput extends BaseInput {
  const RemoveMemberFromChannelInput({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
  });

  final String userId;
  final String channelId;
  final String workspaceId;
}

class RemoveMemberFromChannelOutput extends BaseOutput {
  const RemoveMemberFromChannelOutput({
    required this.ok,
  });

  final bool ok;
}
