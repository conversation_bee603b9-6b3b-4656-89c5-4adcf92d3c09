import 'package:injectable/injectable.dart';
import 'package:member_api/member_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LeaveChannelUseCase
    extends BaseFutureUseCase<LeaveChannelInput, LeaveChannelOutput> {
  const LeaveChannelUseCase(this._memberRepository);

  final MemberRepository _memberRepository;

  @override
  Future<LeaveChannelOutput> buildUseCase(
    LeaveChannelInput input,
  ) async {
    try {
      final bodyBuilder = V3LeaveChannelRequestBuilder();
      bodyBuilder
        ..workspaceId = input.workspaceId
        ..channelId = input.channelId;
      final response = await MemberClient().instance.leaveChannel(
            body: bodyBuilder.build(),
          );
      final ok = response.data?.ok ?? false;
      if (ok) {
        _memberRepository.delete(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
          userId: Config.getInstance().activeSessionKey ?? '',
        );
      }
      return LeaveChannelOutput(ok: ok);
    } catch (ex) {
      Log.e(ex);
    }
    return LeaveChannelOutput(ok: false);
  }
}

class LeaveChannelInput extends BaseInput {
  const LeaveChannelInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String channelId;
  final String workspaceId;
}

class LeaveChannelOutput extends BaseOutput {
  const LeaveChannelOutput({
    required this.ok,
  });

  final bool ok;
}
