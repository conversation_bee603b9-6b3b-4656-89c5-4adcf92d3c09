import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpdateLocalNickNameUseCase extends BaseSyncUseCase<
    UpdateLocalNickNameInput, UpdateLocalNickNameOutput> {
  const UpdateLocalNickNameUseCase(
    this._memberRepository,
  );

  final MemberRepository _memberRepository;

  @override
  UpdateLocalNickNameOutput buildUseCase(UpdateLocalNickNameInput input) {
    _memberRepository.updateNickname(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      userId: input.userId,
      nickname: input.nickname,
    );
    return UpdateLocalNickNameOutput();
  }
}

class UpdateLocalNickNameInput extends BaseInput {
  const UpdateLocalNickNameInput({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.nickname,
  });

  final String userId;
  final String channelId;
  final String workspaceId;
  final String nickname;
}

class UpdateLocalNickNameOutput extends BaseOutput {
  const UpdateLocalNickNameOutput();
}
