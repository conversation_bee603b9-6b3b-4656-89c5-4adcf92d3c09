import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/classes/reaction_data.dart';

@Injectable()
class UpdateReactionUseCase
    extends BaseFutureUseCase<UpdateReactionInput, UpdateReactionOutput> {
  UpdateReactionUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  Future<UpdateReactionOutput> buildUseCase(
    UpdateReactionInput input,
  ) async {
    final messageExist = _messageRepository.getMessageByIdOrRef(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      messageId: input.messageId,
      ref: '',
    );
    if (messageExist == null) {
      return UpdateReactionOutput();
    }

    final reactionsExisted = messageExist.reactions ?? {};
    Map<String, ReactionData> mapReactions = {};
    for (final map in input.reactions) {
      final isReacted = reactionsExisted[map['emoji']!]?.isReacted ?? false;
      mapReactions[map['emoji']!] = ReactionData(
        isReacted: isReacted,
        total: map['total'] as int,
      );
    }
    messageExist.reactionsRaw = jsonEncode(mapReactions);
    _messageRepository.insert(messageExist);
    return UpdateReactionOutput(message: messageExist);
  }
}

class UpdateReactionInput extends BaseInput {
  UpdateReactionInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.reactions,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final List<Map<String, dynamic>> reactions;
}

class UpdateReactionOutput extends BaseOutput {
  UpdateReactionOutput({this.message});

  final Message? message;
}
