import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class EditMessageUseCase
    extends BaseFutureUseCase<EditMessageInput, EditMessageOutput> {
  const EditMessageUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  Future<EditMessageOutput> buildUseCase(
    EditMessageInput input,
  ) async {
    Message m = await _loadFromApi(input);

    _messageRepository.insert(m);

    return EditMessageOutput(message: m);
  }

  Future<Message> _loadFromApi(
    EditMessageInput input,
  ) async {
    var jsonData;
    try {
      if (input.isDm()) {
        final body = V3UpdateDMMessageRequestBuilder()
          ..userId = input.userId
          ..messageId = input.messageId
          ..content = input.content
          ..ref = input.ref
          ..contentLocale = input.contentLocale;
        final response =
            await MessageClient().instance.updateDMMessage(body: body.build());

        jsonData = jsonDecode(
          standardSerializers.toJson(
            V3UpdateDMMessageResponse.serializer,
            response.data,
          ),
        );
      } else {
        final body = V3UpdateMessageRequestBuilder()
          ..workspaceId = input.workspaceId
          ..channelId = input.channelId
          ..messageId = input.messageId
          ..content = input.content
          ..ref = input.ref
          ..contentLocale = input.contentLocale;
        final response =
            await MessageClient().instance.updateMessage(body: body.build());
        jsonData = jsonDecode(
          standardSerializers.toJson(
            V3UpdateMessageResponse.serializer,
            response.data,
          ),
        );
      }
      Map<String, dynamic> data = jsonData['data'];
      Map<String, dynamic> includes = jsonData['includes'];
      ResponseIncludes responseIncludes = ResponseIncludes.fromJson(includes);
      var jsonMessage = data['message'];

      return Future.value(
        MessageSerializer.serializeFromJson(
          data: jsonMessage,
          includes: responseIncludes.toJson(),
        )!,
      );
    } catch (ex) {
      throw ex;
    }
  }
}

class EditMessageInput extends BaseInput {
  EditMessageInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.messageId,
    this.content,
    this.ref,
    this.contentLocale,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;
  final String? content;
  final String? ref;
  final String? contentLocale;

  bool isDm() => userId != null;
}

class EditMessageOutput extends BaseOutput {
  EditMessageOutput({required this.message});

  final Message? message;
}
