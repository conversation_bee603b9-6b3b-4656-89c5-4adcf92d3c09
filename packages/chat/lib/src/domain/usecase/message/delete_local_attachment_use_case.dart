import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteLocalAttachmentUseCase extends BaseSyncUseCase<
    DeleteLocalAttachmentInput, DeleteLocalAttachmentOutput> {
  DeleteLocalAttachmentUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  DeleteLocalAttachmentOutput buildUseCase(DeleteLocalAttachmentInput input) {
    final messageUpdated = _messageRepository.deleteAttachmentByRefOrId(
      attachmentId: input.attachmentId,
      attachmentRef: input.attachmentRef,
    );
    return DeleteLocalAttachmentOutput(message: messageUpdated);
  }
}

class DeleteLocalAttachmentInput extends BaseInput {
  DeleteLocalAttachmentInput({
    this.attachmentRef,
    this.attachmentId,
  }) : assert(attachmentRef != null || attachmentId != null);

  final String? attachmentId;
  final String? attachmentRef;
}

class DeleteLocalAttachmentOutput extends BaseOutput {
  DeleteLocalAttachmentOutput({
    this.message,
  });

  final Message? message;
}
