import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class ToggleMessageReactionUseCase extends BaseFutureUseCase<
    ToggleMessageReactionInput, ToggleMessageReactionOutput> {
  ToggleMessageReactionUseCase(this._messageRepository);

  final MessageRepository _messageRepository;

  @override
  Future<ToggleMessageReactionOutput> buildUseCase(
    ToggleMessageReactionInput input,
  ) async {
    try {
      bool ok = false;
      V3Message? messageData;
      if (input.isAdd) {
        final bodyBuilder = V3AddMessageReactionRequestBuilder();
        bodyBuilder.channelId = input.channelId;
        bodyBuilder.workspaceId = input.workspaceId;
        bodyBuilder.messageId = input.messageId;
        bodyBuilder.emoji = input.emoji;
        final response = await MessageClient()
            .instance
            .addMessageReaction(body: bodyBuilder.build());
        ok = response.data?.ok ?? false;
        if (ok) {
          messageData = response.data?.data?.message;
        }
      } else {
        final bodyBuilder = V3RevokeMessageReactionRequestBuilder();
        bodyBuilder.channelId = input.channelId;
        bodyBuilder.workspaceId = input.workspaceId;
        bodyBuilder.messageId = input.messageId;
        bodyBuilder.emoji = input.emoji;
        final response = await MessageClient()
            .instance
            .revokeMessageReaction(body: bodyBuilder.build());
        ok = response.data?.ok ?? false;
        if (ok) {
          messageData = response.data?.data?.message;
        }
      }
      if (messageData != null) {
        final jsonData = jsonDecode(
          standardSerializers.toJson(
            V3Message.serializer,
            messageData,
          ),
        );
        final message = MessageSerializer.serializeFromJson(data: jsonData)!;
        _messageRepository.insert(message);
        return ToggleMessageReactionOutput(ok: ok, message: message);
      }
    } catch (ex) {
      Log.e(ex, name: 'REACTION');
    }
    return ToggleMessageReactionOutput(ok: false);
  }
}

class ToggleMessageReactionInput extends BaseInput {
  ToggleMessageReactionInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.emoji,
    this.isAdd = true,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final String emoji;
  final bool isAdd;
}

class ToggleMessageReactionOutput extends BaseOutput {
  ToggleMessageReactionOutput({
    required this.ok,
    this.message,
  });

  final bool ok;
  final Message? message;
}
