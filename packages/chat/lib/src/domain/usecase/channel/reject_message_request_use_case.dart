import 'package:channel_api/channel_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class RejectMessageRequestUseCase extends BaseFutureUseCase<
    RejectMessageRequestInput, RejectMessageRequestOutput> {
  RejectMessageRequestUseCase();

  @override
  Future<RejectMessageRequestOutput> buildUseCase(
    RejectMessageRequestInput input,
  ) async {
    try {
      final bodyBuilder = V3RejectMessageRequestRequestBuilder();
      bodyBuilder.userId = input.userId;
      final response = await ChannelClient()
          .instance
          .rejectMessageRequest(body: bodyBuilder.build());
      return RejectMessageRequestOutput(ok: response.data?.ok ?? false);
    } catch (ex) {
      Log.e(ex);
    }
    return RejectMessageRequestOutput(ok: true);
  }
}

class RejectMessageRequestInput extends BaseInput {
  RejectMessageRequestInput({
    required this.userId,
  });

  final String userId;
}

class RejectMessageRequestOutput extends BaseOutput {
  RejectMessageRequestOutput({
    required this.ok,
  });

  final bool ok;
}
