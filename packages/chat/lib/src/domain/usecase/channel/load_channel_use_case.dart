import 'dart:convert';

import 'package:channel_view_api/channel_view_api.dart' as view;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/channel_repository.dart';
import '../../../data/repositories/database/entities/channel.dart';
import '../../../data/repositories/source/api/client/clients.dart';
import '../../../serializers/channel_serializer.dart';

@Injectable()
class LoadChannelUseCase
    extends BaseFutureUseCase<LoadChannelInput, LoadChannelOutput> {
  LoadChannelUseCase(
    this._channelRepository,
  );

  final ChannelRepository _channelRepository;

  @override
  Future<LoadChannelOutput> buildUseCase(LoadChannelInput input) async {
    Channel? channel;

    try {
      dynamic json;
      if (input.userId == null) {
        final response = await ChannelViewClient().instance.getChannel(
              workspaceId: input.workspaceId,
              channelId: input.channelId,
            );

        json = jsonDecode(
          view.standardSerializers.toJson(
            view.V3GetChannelResponse.serializer,
            response.data,
          ),
        );
      } else {
        final response = await ChannelViewClient().instance.getDMChannel(
              userId: input.userId,
            );

        json = jsonDecode(
          view.standardSerializers.toJson(
            view.V3GetDMChannelResponse.serializer,
            response.data,
          ),
        );
      }
      var apiResponse = APIResponse.fromJson(json);
      if (apiResponse.ok) {
        channel = ChannelSerializer.serializeFromJson(
          data: apiResponse.data!.channel!.toJson(),
          metadata: apiResponse.data!.channelMetadata?.toJson(),
          includes: apiResponse.includes!.toJson(),
        );
      }

      if (channel != null) {
        _channelRepository.insert(channel);
      }
      return LoadChannelOutput(channel: channel);
    } catch (e) {
      return LoadChannelOutput(channel: channel);
    }
  }
}

class LoadChannelInput extends BaseInput {
  LoadChannelInput({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class LoadChannelOutput extends BaseOutput {
  LoadChannelOutput({
    required this.channel,
  });

  final Channel? channel;
}
