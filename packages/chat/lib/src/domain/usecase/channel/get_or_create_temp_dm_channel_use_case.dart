import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/channel_type.dart';
import '../../../data/repositories/database/enums/dm_status.dart';

@Injectable()
class GetOrCreateTempDMChannelUseCase extends BaseFutureUseCase<
    GetOrCreateTempDMChannelInput, GetOrCreateTempDMChannelOutput> {
  GetOrCreateTempDMChannelUseCase(
    this._channelRepository,
    this._chatUserRepository,
    this._getChatUserUseCase,
  );

  final ChannelRepository _channelRepository;
  final ChatUserRepository _chatUserRepository;
  final GetChatUserUseCase _getChatUserUseCase;

  @override
  Future<GetOrCreateTempDMChannelOutput> buildUseCase(
    GetOrCreateTempDMChannelInput input,
  ) async {
    String userId = input.userId;
    Channel? channel = _channelRepository.getDMChannel(recipientId: userId);

    if (channel == null) {
      var user = _chatUserRepository.getUser(userId);

      if (user == null) {
        user = (await _getChatUserUseCase
                .execute(GetChatUserInput(userId: userId)))
            .user;
      }

      if (user != null) {
        channel = Channel(
          workspaceId: '0',
          channelId: UUIDUtils.random(),
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          recipientId: userId,
          name: user.dmChannelName,
          avatar: user.profile != null ? user.profile!.avatar : '',
          type: ChannelTypeEnum.DM,
          dmStatus: DMStatusEnum.CONTACTED,
          isTemp: true,
        );
      }
    }

    return GetOrCreateTempDMChannelOutput(channel: channel);
  }
}

class GetOrCreateTempDMChannelInput extends BaseInput {
  GetOrCreateTempDMChannelInput({
    required this.userId,
  });

  final String userId;
}

class GetOrCreateTempDMChannelOutput extends BaseOutput {
  GetOrCreateTempDMChannelOutput({
    required this.channel,
  });

  final Channel? channel;
}
