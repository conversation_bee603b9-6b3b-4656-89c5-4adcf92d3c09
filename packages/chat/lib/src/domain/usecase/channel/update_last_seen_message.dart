import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/channel_metadata_repository.dart';

@Injectable()
class UpdateLastSeenMessageUseCase extends BaseSyncUseCase<
    UpdateLastSeenMessageInput, UpdateLastSeenMessageOutput> {
  UpdateLastSeenMessageUseCase(this._channelRepository);

  final ChannelMetaDataRepository _channelRepository;

  @override
  UpdateLastSeenMessageOutput buildUseCase(
    UpdateLastSeenMessageInput input,
  ) {
    _channelRepository.updateLastSeenMessageId(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      lastSeenMessageId: input.lastSeenMessageId,
    );
    return UpdateLastSeenMessageOutput();
  }
}

class UpdateLastSeenMessageInput extends BaseInput {
  UpdateLastSeenMessageInput(
    this.workspaceId,
    this.channelId,
    this.lastSeenMessageId,
  );

  final String workspaceId;
  final String channelId;
  final String lastSeenMessageId;
}

class UpdateLastSeenMessageOutput extends BaseOutput {
  UpdateLastSeenMessageOutput();
}
