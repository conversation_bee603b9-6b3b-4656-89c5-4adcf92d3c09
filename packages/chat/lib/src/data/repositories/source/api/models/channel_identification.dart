import 'package:json_annotation/json_annotation.dart';

part 'channel_identification.g.dart';

@JsonSerializable(explicitToJson: true)
class ChannelIdentification {
  ChannelIdentification({
    required this.workspaceId,
    required this.channelId,
    this.type,
  });

  factory ChannelIdentification.fromJson(Map<String, dynamic> json) =>
      _$ChannelIdentificationFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelIdentificationToJson(this);
  final String workspaceId;
  final String channelId;
  final ChannelDeletedTypeEnum? type;
}

enum ChannelDeletedTypeEnum {
  @JsonValue(0)
  CHANNEL_DELETED,
  @JsonValue(1)
  MESSAGES_DELETED,
  @JsonValue(2)
  CHANNEL_BLOCKED,
}
