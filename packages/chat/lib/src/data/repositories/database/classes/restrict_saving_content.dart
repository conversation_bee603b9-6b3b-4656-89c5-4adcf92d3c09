import 'package:json_annotation/json_annotation.dart';

part 'restrict_saving_content.g.dart';

@JsonSerializable(explicitToJson: true)
class RestrictSavingContent {
  final bool enable;

  RestrictSavingContent({required this.enable});

  factory RestrictSavingContent.fromJson(Map<String, dynamic> json) =>
      _$RestrictSavingContentFromJson(json);

  Map<String, dynamic> toJson() => _$RestrictSavingContentToJson(this);
}
