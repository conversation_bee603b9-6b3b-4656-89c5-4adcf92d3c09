import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

part 'channel_metadata.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class ChannelMetadata {
  ChannelMetadata({
    this.lastMessageContent,
    this.lastSeenMessageId,
    this.lastMessageId,
    this.unreadCount,
    this.lastMessageContentArguments,
    this.lastMessageMentions,
    this.notificationStatus = true,
  });

  factory ChannelMetadata.fromJson(Map<String, dynamic> json) =>
      _$ChannelMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelMetadataToJson(this);

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 1001)
  String? lastSeenMessageId;

  @Property(uid: 1002)
  int? unreadCount;

  @Property(uid: 1003)
  String? lastMessageId;

  @Property(uid: 1004)
  String? lastMessageContent;

  @Property(uid: 1005)
  List<String>? lastMessageContentArguments;

  @Property(uid: 1006)
  List<int>? permissions;

  @Property(uid: 1007)
  List<String>? lastMessageMentions;

  @Property(uid: 1008)
  bool notificationStatus;

  @Property(uid: 1009)
  int? lastMessageViewTypeRaw;
}

enum ChannelPermissionsEnum {
  OWNER(0),
  CHANNEL_VIEW_CHANNEL(1),
  CHANNEL_MANAGE(2),
  CHANNEL_MEMBERS_MANAGE(3),
  CHANNEL_STICKERS_MANAGE(4),
  CHANNEL_INVITATIONS_MANAGE(5),
  CHANNEL_INVITATIONS_CREATE(6),
  MESSAGES_MANAGE(7),
  MESSAGES_VIEW(8),
  MESSAGES_SEND_MESSAGE(9),
  MESSAGES_SEND_ATTACHMENTS(10),
  MESSAGES_EMBED_LINKS(11),
  MESSAGES_MENTION_EVERYONE(12),
  CHANNEL_VIEW_AUDIT_LOGS(13);

  final int value;

  const ChannelPermissionsEnum(this.value);
}
