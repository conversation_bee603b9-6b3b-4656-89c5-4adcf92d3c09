import 'dart:async';
import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';

import 'cached/chat_user_modification_cache.dart';
import 'chat_user_repository.dart';
import 'database/database.dart';
import 'database/entities/chat_friend_data.dart';
import 'database/entities/chat_user.dart';
import 'database/entities/chat_user_status.dart';
import 'database/entities/member.dart';
import 'database/enums/chat_friend_status.dart';
import 'database/generated/objectbox.g.dart';

@LazySingleton(as: ChatUserRepository)
class ChatUserRepositoryImpl extends ChatUserRepository {
  ChatUserRepositoryImpl(this._store, this._cache);

  final ChatStore _store;
  final ChatUserModificationCache _cache;

  Box<ChatUser> get _userBox => _store.box<ChatUser>();

  @override
  int insert(ChatUser user) {
    if (Config.getInstance().activeSessionKey == null) return -1;
    final usersToUpdate = filterUsersToUpdate([user]);

    if (usersToUpdate.isNotEmpty) {
      _existsAndUpdate(usersToUpdate, keep: true);
      updateCacheForUser(usersToUpdate.first);
      return _userBox.put(usersToUpdate.first);
    }
    return user.id;
  }

  @override
  int forceInsert(ChatUser user) {
    if (Config.getInstance().activeSessionKey == null) return -1;
    _existsAndUpdate([user]);
    updateCacheForUser(user);
    return _userBox.put(user);
  }

  @override
  Future<List<int>> insertAll(List<ChatUser> users) async {
    if (Config.getInstance().activeSessionKey == null) return [];
    final usersToUpdate = filterUsersToUpdate(users);

    if (usersToUpdate.isNotEmpty) {
      _existsAndUpdate(usersToUpdate, keep: true);
      usersToUpdate.forEach(updateCacheForUser);
      return _userBox.putMany(usersToUpdate);
    }
    return users.map((user) => user.id).toList();
  }

  @override
  Future<List<int>> forceInsertAll(List<ChatUser> users) async {
    if (Config.getInstance().activeSessionKey == null) return [];
    _existsAndUpdate(users);
    users.forEach(updateCacheForUser);
    return _userBox.putMany(users);
  }

  List<ChatUser> filterUsersToUpdate(List<ChatUser> users) {
    return users.where(shouldInsertUser).toList();
  }

  void _handleKeepFriendData(ChatUser newUser, ChatUser oldUser) {
    newUser.id = newUser.id;
    if (newUser.chatFriendDataRaw == null) {
      newUser.chatFriendDataRaw = oldUser.chatFriendDataRaw;
    }
  }

  bool shouldInsertUser(ChatUser user) {
    final cachedTime = _cache.peekCached(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: user.userId,
    );
    return cachedTime !=
        '${user.updateTime}/${user.presenceData?.lastUpdateTime ?? ''}';
  }

  void updateCacheForUser(ChatUser user) {
    if (user.updateTime == null || user.partial == true) return;

    _cache.setCache(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: user.userId,
      updateTime: user.updateTime!,
      presenceUpdateTime: user.presenceData?.lastUpdateTime,
    );
  }

  @override
  Stream<List<ChatUser>> getUsersStream() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox
        .query(ChatUser_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<ChatUser> getUsers() {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userBox
        .query(ChatUser_.sessionKey.equals(sessionKey))
        .build()
        .find();
  }

  @override
  ChatUser? getUser(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  ChatUser? getUserByUsername(String userName) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.username.equals(userName),
        )
        .build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  bool deleteUser(String userId) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final query = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.userId.equals(userId),
        )
        .build();
    final session = query.findFirst();
    if (session != null) {
      return _userBox.remove(session.id);
    }
    return false;
  }

  void _existsAndUpdate(List<ChatUser> users, {bool? keep}) {
    removeDuplicateUsers(users);
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey).and(
                ChatUser_.userId
                    .oneOf(users.map((u) => u.userId).toSet().toList()),
              ),
        )
        .build();

    final existingUsers = existingQuery.find();
    existingQuery.close();
    if (existingUsers.length == 0) return;

    for (final user in users) {
      final existingUserIndex = existingUsers.indexWhere(
        (e) => e.userId == user.userId,
      );
      if (existingUserIndex < 0) continue;
      final existingUser = existingUsers[existingUserIndex];
      user.id = existingUser.id;
      if (user.notificationStatus == null) {
        user.notificationStatus = existingUser.notificationStatus;
      }
      if (user.blocked == null) {
        user.blocked = existingUser.blocked;
      }
      if (keep == true) {
        _handleKeepFriendData(user, existingUser);
      }
      if (user.partial == true) {
        user.statusData = existingUser.statusData;
        user.blocked = existingUser.blocked;
      }
    }
  }

  void removeDuplicateUsers(List<ChatUser> users) {
    final seenUserIds = <String>{};
    users.removeWhere((user) => !seenUserIds.add(user.userId));
  }

  @override
  Stream<List<ChatUser>> getAllUsersOnChannelStream(
    String workspaceId,
    String channelId,
  ) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';

    final memberQuery = _store
        .box<Member>()
        .query(
          Member_.workspaceId.equals(workspaceId) &
              Member_.channelId.equals(channelId) &
              Member_.sessionKey.equals(sessionKey),
        )
        .watch(triggerImmediately: true);

    final memberStream = memberQuery
        .map((query) => query.find().map((member) => member.userId).toList());

    return memberStream.asyncMap((userIds) {
      return _userBox
          .query(
            ChatUser_.sessionKey.equals(sessionKey) &
                ChatUser_.userId.oneOf(userIds),
          )
          .build()
          .find();
    });
  }

  @override
  Stream<List<ChatUser>> getAllUsersOnDMChannelStream(
    String userId,
  ) {
    String sessionKey = Config.getInstance().activeSessionKey!;

    final watchedQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.userId.oneOf([sessionKey, userId]),
        )
        .watch(triggerImmediately: true);

    final watchedStream = watchedQuery
        .map((query) => query.find().map((chatUser) => chatUser).toList());

    return watchedStream;
  }

  @override
  Stream<List<ChatUser>> getAllUsersBySetUserIdOnChannelStream(
    Set<String> setUserId,
  ) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.userId.oneOf(setUserId.toList()),
        )
        .watch(triggerImmediately: true);

    final watchedStream = watchedQuery
        .map((query) => query.find().map((chatUser) => chatUser).toList());
    return watchedStream;
  }

  @override
  Stream<List<ChatUser>> getAllUsersBySetUserIdOnFriendListStream(
    Set<String> setUserId,
  ) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';

    final watchedQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey) &
              ChatUser_.userId.oneOf(setUserId.toList()),
        )
        .watch(triggerImmediately: true);

    final watchedStream = watchedQuery
        .map((query) => query.find().map((chatUser) => chatUser).toList());

    return watchedStream;
  }

  @override
  StreamSubscription observerChatUser({
    String? chatUserId,
    String? chatUserName,
    required void Function(ChatUser? chatUser) listener,
  }) {
    assert(
      chatUserId != null || chatUserName != null,
      'Either chatUserId or chatUserName must be provided.',
    );

    final queryCondition = chatUserId != null
        ? ChatUser_.userId.equals(chatUserId)
        : ChatUser_.username.equals(chatUserName!);

    final watchedQuery = _userBox
        .query(
          queryCondition.and(
            ChatUser_.sessionKey
                .equals(Config.getInstance().activeSessionKey ?? ''),
          ),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((chatUserQuery) {
      final chatUser = chatUserQuery.findFirst();
      listener(chatUser);
    });
  }

  @override
  StreamSubscription observerFriendRequest({
    required void Function(List<ChatUser> chatUser) listener,
  }) {
    final raw = jsonEncode(
      ChatFriendData(
        status: ChatFriendStatusEnumExtension.getEnumByValue(
          3,
        ),
      ).toJson(),
    );
    final watchedQuery = _userBox
        .query(
          ChatUser_.chatFriendDataRaw.equals(raw),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((chatUserQuery) {
      final chatUsers = chatUserQuery.find();
      listener(chatUsers);
    });
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _userBox.query(ChatUser_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }

  @override
  StreamSubscription observerMe(void Function(ChatUser? chatUser) listener) {
    final queryCondition =
        ChatUser_.userId.equals(Config.getInstance().activeSessionKey ?? '');

    final watchedQuery = _userBox
        .query(
          queryCondition.and(
            ChatUser_.sessionKey
                .equals(Config.getInstance().activeSessionKey ?? ''),
          ),
        )
        .watch(triggerImmediately: true);

    return watchedQuery.listen((chatUserQuery) {
      final chatUser = chatUserQuery.findFirst();
      listener(chatUser);
    });
  }

  @override
  int updateAvatar(String userId, String avatarPath) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey).and(
                ChatUser_.userId.equals(userId),
              ),
        )
        .build();

    final existingUser = existingQuery.findFirst();
    existingQuery.close();
    if (existingUser != null) {
      existingUser.profile?.avatar = avatarPath;
      existingUser.profile?.originalAvatar = avatarPath;
      return _userBox.put(existingUser);
    }
    return 0;
  }

  @override
  int updateNotificationChannel({
    required String userId,
    required bool isNotification,
  }) {
    String sessionKey = Config.getInstance().activeSessionKey ?? '';
    final existingQuery = _userBox
        .query(
          ChatUser_.sessionKey.equals(sessionKey).and(
                ChatUser_.userId.equals(userId),
              ),
        )
        .build();

    final existingUser = existingQuery.findFirst();
    existingQuery.close();
    if (existingUser != null) {
      existingUser.notificationStatus = isNotification;
      return _userBox.put(existingUser);
    }
    return 0;
  }

  @override
  List<ChatUser> getManyUsers(List<String> userIds) {
    String sessionKey = Config.getInstance().activeSessionKey!;
    final query = _userBox
        .query(
          ChatUser_.sessionKey
              .equals(sessionKey)
              .and(ChatUser_.userId.oneOf(userIds)),
        )
        .build();
    final users = query.find();
    query.close();
    return users;
  }

  @override
  void updateMyStatus(ChatUserStatus? status) {
    final me = getUser(Config.getInstance().activeSessionKey ?? '');
    if (me != null) {
      me.statusData = status;
      _userBox.put(me);
    }
  }
}
