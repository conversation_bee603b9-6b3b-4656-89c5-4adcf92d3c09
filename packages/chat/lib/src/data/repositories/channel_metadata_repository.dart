import '../../../chat.dart';

abstract class ChannelMetaDataRepository {
  void updateChannelMetadata({
    required String workspaceId,
    required String channelId,
    bool? isNotification,
    Message? lastMessage,
    String? lastSeenMessageId,
    int newMessageCount = 0,
  });

  Channel keepMetadata({
    required Channel channel,
    required Channel existingChannel,
  });

  void removeLastMessage({
    required String workspaceId,
    required String channelId,
  });

  Message? getLastMessageOfChannel({
    required String workspaceId,
    required String channelId,
  });

  void updateLastSeenMessageId({
    required String workspaceId,
    required String channelId,
    required String lastSeenMessageId,
  });

  void deleteChannelMetadata({
    Channel? channel,
    String? workspaceId,
    String? channelId,
  });
}
