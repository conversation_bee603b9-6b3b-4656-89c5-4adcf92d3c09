import 'dart:async';

import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

@LazySingleton()
class StreamUnreadMessages {
  StreamUnreadMessages(this._channelRepository);

  StreamSubscription? _channelsSubscription;
  final ChannelRepository _channelRepository;

  void listenUnReadMessage() {
    _channelsSubscription?.cancel();
    _channelsSubscription = _channelRepository.observerListChannels((channels){
      final total = channels.fold(0, (sum, item) => sum + (item.countNew??0));
      AppEventBus.publish(QuantityUnReadMessageEvent(number: total));
    });
  }

  Future<void> close() async {
    await _channelsSubscription?.cancel();
  }
}
