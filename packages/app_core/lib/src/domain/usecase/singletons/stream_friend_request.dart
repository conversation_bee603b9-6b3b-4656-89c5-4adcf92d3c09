import 'dart:async';

import 'package:chat/chat.dart' hide Config;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

@LazySingleton()
class StreamFriendRequest {
  StreamFriendRequest(this._chatFriendRepository, this._chatUserRepository);

  StreamSubscription? _friendSubscription;
  final ChatFriendRepository _chatFriendRepository;

  final List<UserPrivateData> usersPrivateData = [];
  final ChatUserRepository _chatUserRepository;

  void listenFriendRequest() {
    final meID = Config.getInstance().activeSessionKey;
    _friendSubscription?.cancel();
    _friendSubscription = _chatFriendRepository.observerFriendRequests(
      listener: (List<ChatFriend> chatFriends) {
        var setIds = chatFriends
            .map(
              (friend) => friend.participantIds!.firstWhere(
                (id) => id != meID,
              ),
            )
            .toList();
        var _users = _chatUserRepository.getManyUsers(setIds);
        _users.removeWhere((item) => item.username == GlobalConfig.ghost);
        AppEventBus.publish(
          QuantityFriendRequestEvent(number: _users.length),
        );
      },
    );
  }

  Future<void> close() async {
    await _friendSubscription?.cancel();
  }
}
