import 'dart:async';

import 'package:injectable/injectable.dart';

import '../../../../core.dart';

@LazySingleton()
class StreamUsersPrivateData {
  StreamUsersPrivateData(this._privateDataRepository);

  StreamSubscription? _streamSubscription;
  late PrivateDataRepository _privateDataRepository;

  final List<UserPrivateData> usersPrivateData = [];

  void listenUserPrivateData() {
    _streamSubscription?.cancel();
    _streamSubscription =
        _privateDataRepository.getStreamUsers().listen((users) {
      usersPrivateData.clear();
      usersPrivateData.addAll(users);
    });
  }

  Future<void> close() async {
    await _streamSubscription?.cancel();
  }
}
