import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'get_channel_private_data_use_case.freezed.dart';

@Injectable()
class GetChannelPrivateDataUseCase extends BaseFutureUseCase<
    GetChannelPrivateDataUseCaseInput, GetChannelPrivateDataUseCaseOutput> {
  const GetChannelPrivateDataUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<GetChannelPrivateDataUseCaseOutput> buildUseCase(
    GetChannelPrivateDataUseCaseInput input,
  ) async {
    try {
      final newPrivateData = _repository.getPrivateData();

      if (newPrivateData != null) {
        return GetChannelPrivateDataUseCaseOutput(data: newPrivateData);
      }
      return GetChannelPrivateDataUseCaseOutput(data: null);
    } on Exception catch (_) {
      return GetChannelPrivateDataUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class GetChannelPrivateDataUseCaseInput extends BaseInput
    with _$GetChannelPrivateDataUseCaseInput {
  const GetChannelPrivateDataUseCaseInput._();
  factory GetChannelPrivateDataUseCaseInput() =
      _GetChannelPrivateDataUseCaseInput;
}

@freezed
sealed class GetChannelPrivateDataUseCaseOutput extends BaseOutput
    with _$GetChannelPrivateDataUseCaseOutput {
  const GetChannelPrivateDataUseCaseOutput._();
  factory GetChannelPrivateDataUseCaseOutput({
    @Default(null) PrivateData? data,
  }) = _GetChannelPrivateDataUseCaseOutput;
}
