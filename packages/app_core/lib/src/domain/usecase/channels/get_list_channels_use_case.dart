import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class GetListChannelsShareToUseCase extends BaseSyncUseCase<
    GetListChannelsShareToInput, GetListChannelsShareToOutput> {
  GetListChannelsShareToUseCase(
    this._chatRepository,
  );

  final ChannelRepository _chatRepository;

  @override
  GetListChannelsShareToOutput buildUseCase(GetListChannelsShareToInput input) {
    final channels = _chatRepository.getAllChannel(
      limit: input.limit,
      offset: input.offset,
    );
    return GetListChannelsShareToOutput(
      channels: channels,
      hasNext: channels.length == input.limit,
      nextPageToken: null,
    );
  }
}

class GetListChannelsShareToInput extends BaseInput {
  GetListChannelsShareToInput({
    this.limit = 50,
    this.offset = 0,
    this.nextPageToken,
  });

  final int limit;
  final int offset;
  final String? nextPageToken;
}

class GetListChannelsShareToOutput extends BaseOutput {
  GetListChannelsShareToOutput({
    required this.channels,
    required this.hasNext,
    this.nextPageToken,
  });

  final List<Channel> channels;
  final bool hasNext;
  final String? nextPageToken;
}
