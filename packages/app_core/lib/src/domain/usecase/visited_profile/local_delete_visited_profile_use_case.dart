import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

part 'local_delete_visited_profile_use_case.freezed.dart';

@Injectable()
class LocalDeleteVisitedProfileUseCase extends BaseFutureUseCase<
    LocalDeleteVisitedProfileUseCaseInput,
    LocalDeleteVisitedProfileUseCaseOutput> {
  const LocalDeleteVisitedProfileUseCase(this._visitedProfileRepository);

  final VisitedProfileRepository _visitedProfileRepository;

  @protected
  @override
  Future<LocalDeleteVisitedProfileUseCaseOutput> buildUseCase(
    LocalDeleteVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result =
          _visitedProfileRepository.deleteVisitedProfile(input.userId);

      return LocalDeleteVisitedProfileUseCaseOutput(response: result);
    } on Exception catch (_) {
      return LocalDeleteVisitedProfileUseCaseOutput(response: false);
    }
  }
}

@freezed
sealed class LocalDeleteVisitedProfileUseCaseInput extends BaseInput
    with _$LocalDeleteVisitedProfileUseCaseInput {
  const LocalDeleteVisitedProfileUseCaseInput._();
  factory LocalDeleteVisitedProfileUseCaseInput(String userId) =
      _LocalDeleteVisitedProfileUseCaseInput;
}

@freezed
sealed class LocalDeleteVisitedProfileUseCaseOutput extends BaseOutput
    with _$LocalDeleteVisitedProfileUseCaseOutput {
  const LocalDeleteVisitedProfileUseCaseOutput._();
  factory LocalDeleteVisitedProfileUseCaseOutput({bool? response}) =
      _LocalDeleteVisitedProfileUseCaseOutput;
}
