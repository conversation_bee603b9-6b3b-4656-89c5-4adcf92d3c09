import 'dart:convert';

import 'package:chat/chat.dart' as chat;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../common/di/di.dart';
import '../upsert_user_use_case.dart';

part 'load_visited_profile_use_case.freezed.dart';

@Injectable()
class LoadVisitedProfileUseCase extends BaseFutureUseCase<
    LoadVisitedProfileUseCaseInput, LoadVisitedProfileUseCaseOutput> {
  const LoadVisitedProfileUseCase();

  @protected
  @override
  Future<LoadVisitedProfileUseCaseOutput> buildUseCase(
    LoadVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result =
          await UserViewClient().instance.listUserVisitedProfile(limit: 500);
      List<V3VisitedProfileData> getListVisitedProfile =
          result.data?.data?.toList() ?? [];
      List<User> listUser = [];
      List<VisitedProfile> listVisitedProfile = [];
      getListVisitedProfile.forEach((item) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3VisitedProfileData.serializer,
            item,
          ),
        );
        json["user"]["sessionKey"] =
            Config.getInstance().activeSessionKey ?? '';
        var user = User.fromJson(json["user"]);

        ///  update status data from current local  because response null
        user.partial = true;
        var chatUser = chat.ChatUser.fromJson(user.toJson());

        ///  update status data from current local because response null
        chatUser.partial = true;
        getIt<UpsertUserAndChatUserUseCase>().execute(
          UpsertUserAndChatUserInput(
            user: user,
            chatUser: chatUser,
          ),
        );
        listUser.add(user);
        var visitedProfile = VisitedProfile(
          sessionKey: user.sessionKey,
          userId: user.userId,
          createTime: json["visitedTime"],
          updateTime: json["lastVisitedTime"],
          isRead: true,
        );
        listVisitedProfile.add(visitedProfile);
      });

      return LoadVisitedProfileUseCaseOutput(
        listVisitedProfile: listVisitedProfile,
        listUser: listUser,
      );
    } on Exception catch (_) {
      return LoadVisitedProfileUseCaseOutput(
        listVisitedProfile: null,
        listUser: null,
      );
    }
  }
}

@freezed
sealed class LoadVisitedProfileUseCaseInput extends BaseInput
    with _$LoadVisitedProfileUseCaseInput {
  const LoadVisitedProfileUseCaseInput._();
  factory LoadVisitedProfileUseCaseInput() = _LoadVisitedProfileUseCaseInput;
}

@freezed
sealed class LoadVisitedProfileUseCaseOutput extends BaseOutput
    with _$LoadVisitedProfileUseCaseOutput {
  const LoadVisitedProfileUseCaseOutput._();
  factory LoadVisitedProfileUseCaseOutput({
    required List<VisitedProfile>? listVisitedProfile,
    required List<User>? listUser,
  }) = _LoadVisitedProfileUseCaseOutput;
}
