import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/preferences/app_preferences_repository.dart';

@Injectable()
class InitAppUseCase extends BaseSyncUseCase<InitAppInput, InitAppOutput> {
  InitAppUseCase(this._repository);

  final AppPreferencesRepository _repository;

  @override
  InitAppOutput buildUseCase(InitAppInput input) {
    final locale = _repository.getLocale;
    final themeMode = _repository.getThemeMode;
    return InitAppOutput(locale: locale, themeMode: themeMode);
  }
}

class InitAppInput extends BaseInput {
  InitAppInput();
}

class InitAppOutput extends BaseOutput {
  InitAppOutput({required this.locale, required this.themeMode});

  final Locale locale;
  final ThemeMode themeMode;
}
