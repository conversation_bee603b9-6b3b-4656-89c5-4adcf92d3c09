import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:injectable/injectable.dart';

import '../../data/extensions/connectivity_result_extension.dart';

@lazySingleton
class NetworkManager {
  final Connectivity _connectivity = Connectivity();
  ConnectivityResult _currentStatus = ConnectivityResult.none;

  NetworkManager() {
    _connectivity.onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      _currentStatus = results.firstWhere(
        (result) => !result.isNone,
        orElse: () => ConnectivityResult.none,
      );
    });
  }

  Future<ConnectivityResult> checkConnectivity() async {
    _currentStatus = (await _connectivity.checkConnectivity()).first;
    return _currentStatus;
  }

  Stream<List<ConnectivityResult>> get onConnectivityChanged =>
      _connectivity.onConnectivityChanged;

  bool hasConnection() {
    return !_currentStatus.isNone;
  }

  bool noConnection() {
    return _currentStatus.isNone;
  }

  static bool validNoInternet(ConnectivityResult result) {
    return result == ConnectivityResult.none ? true : false;
  }

  ConnectivityResult get currentStatus => _currentStatus;

  bool get isWifi => _currentStatus.isWifi;

  bool get isBluetooth => _currentStatus.isBluetooth;

  bool get isEthernet => _currentStatus.isEthernet;

  bool get isMobile => _currentStatus.isMobile;

  bool get isNone => _currentStatus.isNone;

  bool get isVpn => _currentStatus.isVpn;

  bool get isOther => _currentStatus.isOther;
}
