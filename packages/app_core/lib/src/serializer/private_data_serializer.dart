import 'package:user_view_api/user_view_api.dart';

import '../../core.dart';

class PrivateDataSerializer {
  static PrivateData? serializeFromJson({
    required Map<String, dynamic> json,
  }) {
    final privateData = PrivateData(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: Config.getInstance().activeSessionKey ?? '',
      createTime: json['createTime'] ?? '',
      updateTime: json['updateTime'] ?? '',
    );

    return privateData
      ..channels.addAll(_parseChannels(privateData, json['channels'] as List?))
      ..users.addAll(_parseUsers(privateData, json['users'] as List?))
      ..callLogs.addAll(_parseCallLogs(privateData, json['callLogs'] as List?));
  }

  static List<ChannelPrivateData> _parseChannels(
    PrivateData privateData,
    List<dynamic>? channels,
  ) {
    return channels?.map((channel) {
          final channelData = channel as Map<String, dynamic>;
          return ChannelPrivateData(
            sessionKey: Config.getInstance().activeSessionKey ?? '',
            channelId: channelData['id'] ?? '',
            version: channelData['version'] ?? 0,
            source: channelData['source'] ?? '',
            unreadCount: channelData['unreadCount'] ?? 0,
            lastSeenMessageId: channelData['lastSeenMessageId'],
            pinned: channelData['pinned'] ?? false,
            sort: channelData['sort'] ?? 0,
          )..privateData.target = privateData;
        }).toList() ??
        [];
  }

  static List<UserPrivateData> _parseUsers(
    PrivateData privateData,
    List<dynamic>? users,
  ) {
    return users?.map((user) {
          final userData = user as Map<String, dynamic>;
          return UserPrivateData(
            sessionKey: Config.getInstance().activeSessionKey ?? '',
            userId: userData['id'] ?? '',
            version: userData['version'] ?? 0,
            source: userData['source'] ?? '',
            dmId: userData['dmId'],
            blocked: userData['blocked'] ?? false,
            aliasName: userData['aliasName'],
          )..privateData.target = privateData;
        }).toList() ??
        [];
  }

  static UserPrivateData parseUser(
    Map<String, dynamic> userData,
  ) {
    return UserPrivateData(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      userId: userData['id'] ?? '',
      version: userData['version'] ?? 0,
      source: userData['source'] ?? '',
      dmId: userData['dmId'],
      blocked: userData['blocked'] ?? false,
      aliasName: userData['aliasName'],
    );
  }

  static List<CallLogPrivateData> _parseCallLogs(
    PrivateData privateData,
    List<dynamic>? callLogs,
  ) {
    return callLogs?.map((callLog) {
          final callLogData = callLog as Map<String, dynamic>;
          return CallLogPrivateData(
            sessionKey: Config.getInstance().activeSessionKey ?? '',
            callId: callLogData['id'] ?? 0,
            version: callLogData['version'] ?? 0,
            source: callLogData['source'] ?? '',
            callerId: callLogData['callerId'] ?? '',
            calleeId: callLogData['calleeId'] ?? '',
            callState: _parseEnum<V3CallStateEnum>(
              callLogData['callState'],
              V3CallStateEnum.valueOf,
            ),
            endedReason: _parseEnum<V3CallEndedReasonEnum>(
              callLogData['endedReason'],
              V3CallEndedReasonEnum.valueOf,
            ),
            callTimeInSeconds: callLogData['callTimeInSeconds'] ?? 0,
            isOutgoing: callLogData['isOutgoing'] ?? false,
            readTime: callLogData['readTime'],
            endedTime: callLogData['endedTime'],
            createTime: callLogData['createTime'] ?? '',
          )..privateData.target = privateData;
        }).toList() ??
        [];
  }

  static int _parseEnum<T>(dynamic value, T Function(String) valueOf) {
    try {
      final enumValue = valueOf(value as String);
      if (enumValue is V3CallStateEnum) {
        return enumValue.toWireNumber();
      } else if (enumValue is V3CallEndedReasonEnum) {
        return enumValue.toWireNumber();
      }
    } catch (e) {
      return -1;
    }
    return -1;
  }
}

extension V3CallStateEnumExtension on V3CallStateEnum {
  int toWireNumber() {
    return this == V3CallStateEnum.CALL_STATE_UNSPECIFIED
        ? 0
        : this == V3CallStateEnum.CALL_STATE_DIALING
            ? 1
            : this == V3CallStateEnum.CALL_STATE_CALLING
                ? 2
                : this == V3CallStateEnum.CALL_STATE_READY_TO_CONNECT
                    ? 3
                    : this == V3CallStateEnum.CALL_STATE_CONNECTING
                        ? 4
                        : this == V3CallStateEnum.CALL_STATE_CONNECTED
                            ? 5
                            : this == V3CallStateEnum.CALL_STATE_RECONNECTING
                                ? 6
                                : this == V3CallStateEnum.CALL_STATE_ENDED
                                    ? 10
                                    : -1;
  }
}

extension V3CallEndedReasonEnumExtension on V3CallEndedReasonEnum {
  int toWireNumber() {
    return this == V3CallEndedReasonEnum.CALL_ENDED_REASON_UNSPECIFIED
        ? 0
        : this == V3CallEndedReasonEnum.CALL_ENDED_REASON_FAILED
            ? 1
            : this == V3CallEndedReasonEnum.CALL_ENDED_REASON_REMOTE_ENDED
                ? 2
                : this == V3CallEndedReasonEnum.CALL_ENDED_REASON_UNANSWERED
                    ? 3
                    : this ==
                            V3CallEndedReasonEnum
                                .CALL_ENDED_REASON_ANSWERED_ELSEWHERE
                        ? 4
                        : this ==
                                V3CallEndedReasonEnum
                                    .CALL_ENDED_REASON_DECLINED_ELSEWHERE
                            ? 5
                            : -1;
  }
}
