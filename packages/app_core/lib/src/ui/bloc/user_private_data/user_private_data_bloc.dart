import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'user_private_data_bloc.freezed.dart';
part 'user_private_data_event.dart';
part 'user_private_data_state.dart';

@injectable
class UserPrivateDataBloc
    extends BaseBloc<UserPrivateDataEvent, UserPrivateDataState> {
  UserPrivateDataBloc(this._privateDataRepository)
      : super(UserPrivateDataState.initial()) {
    on<InitUserPrivateDataEvent>(_onInit);
    on<ListUserPrivateDataEvent>(_onListUserPrivateData);
    on<GetPrivateDataUnSubscriptionEvent>(_onGetPrivateDataUnSubscription);
  }

  final PrivateDataRepository _privateDataRepository;
  StreamSubscription? _getPrivateDataUser;

  Future<void> _onInit(
    UserPrivateDataEvent event,
    Emitter<UserPrivateDataState> emit,
  ) async {
    _getPrivateDataUser ??= _privateDataRepository
        .getStreamUsers()
        .listen(onReceivedFromPrivateDataUser);
  }

  Future<void> onReceivedFromPrivateDataUser(
    List<UserPrivateData> listUser,
  ) async {
    add(ListUserPrivateDataEvent(listUserPrivateData: listUser));
  }

  FutureOr<void> _onListUserPrivateData(
    ListUserPrivateDataEvent event,
    Emitter<UserPrivateDataState> emit,
  ) async {
    emit(
      UserPrivateDataState.listUserPrivateData(
        listUserPrivateData: event.listUserPrivateData,
      ),
    );
  }

  FutureOr<void> _onGetPrivateDataUnSubscription(
    GetPrivateDataUnSubscriptionEvent event,
    Emitter<UserPrivateDataState> emit,
  ) {
    if (_getPrivateDataUser != null) {
      _getPrivateDataUser?.cancel();
      _getPrivateDataUser = null;
    }
  }
}
