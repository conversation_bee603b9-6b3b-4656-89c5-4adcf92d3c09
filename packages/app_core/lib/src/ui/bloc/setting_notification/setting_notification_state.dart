part of 'setting_notification_bloc.dart';

@freezed
sealed class SettingNotificationState extends BaseBlocState
    with _$SettingNotificationState {
  const SettingNotificationState._();

  factory SettingNotificationState.initial() = SettingNotificationStateInitial;

  factory SettingNotificationState.subscribeChannel({bool? response}) =
      SettingNotificationStateSubscribeChannel;

  factory SettingNotificationState.unsubscribeChannel({bool? response}) =
      SettingNotificationStateUnsubscribeChannel;

  factory SettingNotificationState.turnOnGlobalNotification({
    bool? response,
  }) = SettingNotificationStateTurnOnGlobalNotification;

  factory SettingNotificationState.turnOffGlobalNotification({
    bool? response,
  }) = SettingNotificationStateTurnOffGlobalNotification;
}

extension SettingNotificationStateX on SettingNotificationState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(bool? response)? subscribeChannel,
    T Function(bool? response)? unsubscribeChannel,
    T Function(bool? response)? turnOnGlobalNotification,
    T Function(bool? response)? turnOffGlobalNotification,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is SettingNotificationStateInitial && initial != null) {
      return initial();
    }
    if (state is SettingNotificationStateSubscribeChannel &&
        subscribeChannel != null) {
      return subscribeChannel(state.response);
    }
    if (state is SettingNotificationStateUnsubscribeChannel &&
        unsubscribeChannel != null) {
      return unsubscribeChannel(state.response);
    }
    if (state is SettingNotificationStateTurnOnGlobalNotification &&
        turnOnGlobalNotification != null) {
      return turnOnGlobalNotification(state.response);
    }
    if (state is SettingNotificationStateTurnOffGlobalNotification &&
        turnOffGlobalNotification != null) {
      return turnOffGlobalNotification(state.response);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(bool? response) subscribeChannel,
    required T Function(bool? response) unsubscribeChannel,
    required T Function(bool? response) turnOnGlobalNotification,
    required T Function(bool? response) turnOffGlobalNotification,
  }) {
    final state = this;

    if (state is SettingNotificationStateInitial) {
      return initial();
    }
    if (state is SettingNotificationStateSubscribeChannel) {
      return subscribeChannel(state.response);
    }
    if (state is SettingNotificationStateUnsubscribeChannel) {
      return unsubscribeChannel(state.response);
    }
    if (state is SettingNotificationStateTurnOnGlobalNotification) {
      return turnOnGlobalNotification(state.response);
    }
    if (state is SettingNotificationStateTurnOffGlobalNotification) {
      return turnOffGlobalNotification(state.response);
    }

    throw StateError('Unhandled SettingNotificationState: $state');
  }
}
