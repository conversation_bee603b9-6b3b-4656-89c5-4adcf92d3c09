import 'dart:async';

import 'package:chat/chat.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

import '../../../../core.dart';

part 'block_user_bloc.freezed.dart';
part 'block_user_event.dart';
part 'block_user_state.dart';

@injectable
class BlockUserBloc extends BaseBloc<BlockUserEvent, BlockUserState> {
  BlockUserBloc(
    this._blockUserUseCase,
    this._unBlockUserUseCase,
    this._getListBlockUserUseCase,
    this._managerRepository,
  ) : super(BlockUserState.initial()) {
    on<OnBlockUserEvent>(_onBlockUser);
    on<OnUnBlockUserEvent>(_onUnBlockUser);
    on<OnLoadListBlockUserEvent>(_onLoadListBlockUser);
    on<OnUpdateCloseWarningEvent>(_onUpdateCloseWarning);
  }

  final BlockUserUseCase _blockUserUseCase;
  final UnBlockUserUseCase _unBlockUserUseCase;
  final GetListBlockUserUseCase _getListBlockUserUseCase;
  final ManagerRepository _managerRepository;

  Future<void> _onLoadListBlockUser(
    OnLoadListBlockUserEvent event,
    Emitter<BlockUserState> emit,
  ) async {
    emit(
      BlockUserState.displayCloseWarning(
        isClose: _managerRepository.getClosedListBlockUserWarningStatus(),
      ),
    );
    GetListBlockUserOutput getListBlockUserOutput =
        await _getListBlockUserUseCase.execute(GetListBlockUserInput());
    emit(
      BlockUserState.loadListBlockUser(
        listBlockUser: getListBlockUserOutput.listBlockUser,
      ),
    );
  }

  Future<void> _onBlockUser(
    OnBlockUserEvent event,
    Emitter<BlockUserState> emit,
  ) async {
    emit(BlockUserState.showProcessDialog());
    BlockUserOutput blockUserOutput =
        await _blockUserUseCase.execute(BlockUserInput(userId: event.userId));

    emit(
      BlockUserState.updateProcessDialog(
        response: blockUserOutput.ok,
        popOnlyMine: event.popOnlyMine,
      ),
    );
  }

  Future<void> _onUnBlockUser(
    OnUnBlockUserEvent event,
    Emitter<BlockUserState> emit,
  ) async {
    emit(BlockUserState.showProcessDialog());
    UnBlockUserOutput unBlockUserOutput = await _unBlockUserUseCase
        .execute(UnBlockUserInput(userId: event.userId));

    emit(
      BlockUserState.updateProcessDialog(
        response: unBlockUserOutput.ok,
        popOnlyMine: event.popOnlyMine,
      ),
    );
  }

  Future<void> _onUpdateCloseWarning(
    OnUpdateCloseWarningEvent event,
    Emitter<BlockUserState> emit,
  ) async {
    emit(BlockUserState.showProcessDialog());
    _managerRepository.updateClosedListBlockUserWarningStatus(event.isClose);

    emit(BlockUserState.displayCloseWarning(isClose: event.isClose));
  }
}
