part of 'report_message_bloc.dart';

@freezed
sealed class ReportMessageState extends BaseBlocState
    with _$ReportMessageState {
  const ReportMessageState._();

  factory ReportMessageState.initial() = ReportMessageInitial;

  factory ReportMessageState.showProcessDialog() =
      ReportMessageStateShowProcessDialog;

  factory ReportMessageState.updateProcessDialog({
    @Default(false) bool response,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? name,
    bool? isBlock,
  }) = _UpdateProcessDialog;
}

extension ReportMessageStateX on ReportMessageState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function()? showProcessDialog,
    T Function(
      bool response,
      String? workspaceId,
      String? channelId,
      String? userId,
      String? name,
      bool? isBlock,
    )? updateProcessDialog,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ReportMessageInitial && initial != null) return initial();
    if (state is ReportMessageStateShowProcessDialog &&
        showProcessDialog != null) {
      return showProcessDialog();
    }
    if (state is _UpdateProcessDialog && updateProcessDialog != null) {
      return updateProcessDialog(
        state.response,
        state.workspaceId,
        state.channelId,
        state.userId,
        state.name,
        state.isBlock,
      );
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function() showProcessDialog,
    required T Function(
      bool response,
      String? workspaceId,
      String? channelId,
      String? userId,
      String? name,
      bool? isBlock,
    ) updateProcessDialog,
  }) {
    final state = this;

    if (state is ReportMessageInitial) return initial();
    if (state is ReportMessageStateShowProcessDialog)
      return showProcessDialog();
    if (state is _UpdateProcessDialog) {
      return updateProcessDialog(
        state.response,
        state.workspaceId,
        state.channelId,
        state.userId,
        state.name,
        state.isBlock,
      );
    }

    throw StateError('Unhandled state: $state');
  }
}
