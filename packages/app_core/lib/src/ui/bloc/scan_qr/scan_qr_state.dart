part of 'scan_qr_bloc.dart';

@freezed
sealed class ScanQrState extends BaseBlocState with _$ScanQrState {
  const ScanQrState._();
  factory ScanQrState.initial() = ScanQrStateInitial;

  factory ScanQrState.processing(String qrData) = Processing;

  factory ScanQrState.done(String qrData) = Done;

  factory ScanQrState.authQRLoginDetected(String qrData) = AuthQRLoginDetected;

  factory ScanQrState.scanToConnectLinkDetected(String qrData) =
      ScanToConnectLinkDetected;

  factory ScanQrState.userConnectLinkDecodeSuccessful(String userId) =
      UserConnectLinkDecodeSuccessful;

  factory ScanQrState.invitationDetected(String qrData) = InvitationDetected;

  factory ScanQrState.invalidQRDetected(String qrData) = InvalidQRDetected;
}

extension ScanQrStateX on ScanQrState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(String qrData)? processing,
    T Function(String qrData)? done,
    T Function(String qrData)? authQRLoginDetected,
    T Function(String qrData)? scanToConnectLinkDetected,
    T Function(String userId)? userConnectLinkDecodeSuccessful,
    T Function(String qrData)? invitationDetected,
    T Function(String qrData)? invalidQRDetected,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ScanQrStateInitial && initial != null) return initial();
    if (state is Processing && processing != null)
      return processing(state.qrData);
    if (state is Done && done != null) return done(state.qrData);
    if (state is AuthQRLoginDetected && authQRLoginDetected != null) {
      return authQRLoginDetected(state.qrData);
    }
    if (state is ScanToConnectLinkDetected &&
        scanToConnectLinkDetected != null) {
      return scanToConnectLinkDetected(state.qrData);
    }
    if (state is UserConnectLinkDecodeSuccessful &&
        userConnectLinkDecodeSuccessful != null) {
      return userConnectLinkDecodeSuccessful(state.userId);
    }
    if (state is InvitationDetected && invitationDetected != null) {
      return invitationDetected(state.qrData);
    }
    if (state is InvalidQRDetected && invalidQRDetected != null) {
      return invalidQRDetected(state.qrData);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(String qrData) processing,
    required T Function(String qrData) done,
    required T Function(String qrData) authQRLoginDetected,
    required T Function(String qrData) scanToConnectLinkDetected,
    required T Function(String userId) userConnectLinkDecodeSuccessful,
    required T Function(String qrData) invitationDetected,
    required T Function(String qrData) invalidQRDetected,
  }) {
    final state = this;

    if (state is ScanQrStateInitial) return initial();
    if (state is Processing) return processing(state.qrData);
    if (state is Done) return done(state.qrData);
    if (state is AuthQRLoginDetected) return authQRLoginDetected(state.qrData);
    if (state is ScanToConnectLinkDetected)
      return scanToConnectLinkDetected(state.qrData);
    if (state is UserConnectLinkDecodeSuccessful)
      return userConnectLinkDecodeSuccessful(state.userId);
    if (state is InvitationDetected) return invitationDetected(state.qrData);
    if (state is InvalidQRDetected) return invalidQRDetected(state.qrData);

    throw StateError('Unhandled state: $state');
  }
}
