import '../../../../core.dart';
import '../../isolate/domain/models/task_model.dart';
import '../../isolate/domain/models/task_priority.dart';

class PriorityTaskQueue {
  final List<TaskModel> _highPriorityTasks = [];
  final List<TaskModel> _mediumPriorityTasks = [];
  final List<TaskModel> _lowPriorityTasks = [];

  // List of heavy tasks (image/video processing)
  final List<String> _heavyTasks = [
    TaskNameEnum.compressVideoMessage.value,
    TaskNameEnum.compressAndUploadImages.value,
    TaskNameEnum.compressAndUploadVideoMessage.value,
  ];

  // Maximum number of tasks of the same type to process at once
  final int _maxConcurrentSameTypeTask = 3;

  void add(TaskModel task) {
    switch (task.priority) {
      case TaskPriority.high:
        _highPriorityTasks.add(task);
        _sortTaskList(_highPriorityTasks);
        break;
      case TaskPriority.medium:
        _mediumPriorityTasks.add(task);
        _sortTaskList(_mediumPriorityTasks);
        break;
      case TaskPriority.low:
        _lowPriorityTasks.add(task);
        _sortTaskList(_lowPriorityTasks);
        break;
    }

    RILogger.printClassMethodDebug(
      'PriorityTaskQueue',
      'add',
      'Added task ${task.id} with priority ${task.priority}. Queue sizes: high=${_highPriorityTasks.length}, medium=${_mediumPriorityTasks.length}, low=${_lowPriorityTasks.length}',
    );
  }

  TaskModel removeFirst() {
    if (isEmpty) {
      throw StateError('Cannot remove from an empty queue');
    }

    // Prioritize reference tasks first
    // Look in high priority queue
    if (_highPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _highPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        final task = _highPriorityTasks.removeAt(referenceTaskIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed high priority reference task ${task.id}',
        );
        return task;
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _highPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        final task = _highPriorityTasks.removeAt(nonHeavyIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed high priority non-heavy task ${task.id}',
        );
        return task;
      }

      // If no light tasks found, get the first task
      final task = _highPriorityTasks.removeAt(0);
      RILogger.printClassMethodDebug(
        'PriorityTaskQueue',
        'removeFirst',
        'Removed high priority task ${task.id}',
      );
      return task;
    }

    // Prioritize medium priority tasks
    if (_mediumPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _mediumPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        final task = _mediumPriorityTasks.removeAt(referenceTaskIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed medium priority reference task ${task.id}',
        );
        return task;
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _mediumPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        final task = _mediumPriorityTasks.removeAt(nonHeavyIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed medium priority non-heavy task ${task.id}',
        );
        return task;
      }

      // If no light tasks found, get the first task
      final task = _mediumPriorityTasks.removeAt(0);
      RILogger.printClassMethodDebug(
        'PriorityTaskQueue',
        'removeFirst',
        'Removed medium priority task ${task.id}',
      );
      return task;
    }

    // Prioritize low priority tasks
    if (_lowPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _lowPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        final task = _lowPriorityTasks.removeAt(referenceTaskIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed low priority reference task ${task.id}',
        );
        return task;
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _lowPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        final task = _lowPriorityTasks.removeAt(nonHeavyIndex);
        RILogger.printClassMethodDebug(
          'PriorityTaskQueue',
          'removeFirst',
          'Removed low priority non-heavy task ${task.id}',
        );
        return task;
      }

      // If no light tasks found, get the first task
      final task = _lowPriorityTasks.removeAt(0);
      RILogger.printClassMethodDebug(
        'PriorityTaskQueue',
        'removeFirst',
        'Removed low priority task ${task.id}',
      );
      return task;
    }

    throw StateError('Unexpected empty queue state');
  }

  bool remove(String taskId) {
    bool removed = false;

    removed = _removeFromList(_highPriorityTasks, taskId) ||
        _removeFromList(_mediumPriorityTasks, taskId) ||
        _removeFromList(_lowPriorityTasks, taskId);

    if (removed) {
      RILogger.printClassMethodDebug(
        'PriorityTaskQueue',
        'remove',
        'Removed task $taskId from queue',
      );
    }

    return removed;
  }

  bool _removeFromList(List<TaskModel> list, String taskId) {
    final index = list.indexWhere((task) => task.id == taskId);
    if (index >= 0) {
      list.removeAt(index);
      return true;
    }
    return false;
  }

  void _sortTaskList(List<TaskModel> list) {
    // First sort by task name to group similar tasks
    // Then sort by creation time (oldest first) within each group
    list.sort((a, b) {
      // First compare by task name
      final nameComparison = a.name.compareTo(b.name);
      if (nameComparison != 0) {
        return nameComparison;
      }

      // If task names are the same, sort by creation time
      return a.createdAt.compareTo(b.createdAt);
    });
  }

  bool get isEmpty =>
      _highPriorityTasks.isEmpty &&
      _mediumPriorityTasks.isEmpty &&
      _lowPriorityTasks.isEmpty;

  bool get isNotEmpty => !isEmpty;

  int get length =>
      _highPriorityTasks.length +
      _mediumPriorityTasks.length +
      _lowPriorityTasks.length;

  void clear() {
    _highPriorityTasks.clear();
    _mediumPriorityTasks.clear();
    _lowPriorityTasks.clear();

    RILogger.printClassMethodDebug(
      'PriorityTaskQueue',
      'clear',
      'Cleared all tasks from queue',
    );
  }

  List<TaskModel> getAll() {
    final allTasks = [
      ..._highPriorityTasks,
      ..._mediumPriorityTasks,
      ..._lowPriorityTasks,
    ];
    return allTasks;
  }

  /// Count the number of tasks with the same name in the queue
  int countTasksByName(String taskName) {
    int count = 0;

    for (var task in _highPriorityTasks) {
      if (task.name == taskName) count++;
    }

    for (var task in _mediumPriorityTasks) {
      if (task.name == taskName) count++;
    }

    for (var task in _lowPriorityTasks) {
      if (task.name == taskName) count++;
    }

    return count;
  }

  /// View the next task without removing it from the queue
  /// Follows the same priority logic as removeFirst()
  TaskModel? peekFirst() {
    if (isEmpty) {
      return null;
    }

    // Prioritize reference tasks first
    // Look in high priority queue
    if (_highPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _highPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        return _highPriorityTasks[referenceTaskIndex];
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _highPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        return _highPriorityTasks[nonHeavyIndex];
      }

      // If no light tasks found, get the first task
      return _highPriorityTasks[0];
    }

    // Prioritize medium priority tasks
    if (_mediumPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _mediumPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        return _mediumPriorityTasks[referenceTaskIndex];
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _mediumPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        return _mediumPriorityTasks[nonHeavyIndex];
      }

      // If no light tasks found, get the first task
      return _mediumPriorityTasks[0];
    }

    // Prioritize low priority tasks
    if (_lowPriorityTasks.isNotEmpty) {
      // Find reference tasks first
      final referenceTaskIndex = _lowPriorityTasks.indexWhere(
        (task) => task.isReferenceTask,
      );

      if (referenceTaskIndex >= 0) {
        return _lowPriorityTasks[referenceTaskIndex];
      }

      // Find tasks that are not image/video processing
      final nonHeavyIndex = _lowPriorityTasks.indexWhere(
        (task) => !_heavyTasks.contains(task.name),
      );

      if (nonHeavyIndex >= 0) {
        return _lowPriorityTasks[nonHeavyIndex];
      }

      // If no light tasks found, get the first task
      return _lowPriorityTasks[0];
    }

    return null;
  }
}
