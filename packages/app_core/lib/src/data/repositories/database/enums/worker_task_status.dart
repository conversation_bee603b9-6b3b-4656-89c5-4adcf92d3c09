enum WorkerTaskStatus {
  pending(0),
  inProgress(1),
  completed(2),
  failed(3);

  final int value;

  const WorkerTaskStatus(this.value);

  static WorkerTaskStatus? getEnumByValue(int? value) {
    if (value == null) return WorkerTaskStatus.pending;
    return WorkerTaskStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => WorkerTaskStatus.pending,
    );
  }

  int rawValue() => value;
}

extension WorkerTaskStatusExtension on WorkerTaskStatus {
  static WorkerTaskStatus fromRawValue(int rawValue) {
    return WorkerTaskStatus.getEnumByValue(rawValue) ??
        WorkerTaskStatus.pending;
  }
}
