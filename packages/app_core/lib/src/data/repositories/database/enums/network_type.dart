enum NetworkTypeEnum {
  notRequired(0),
  connected(1),
  unmetered(2),
  notRoaming(3),
  metered(4);

  final int value;

  const NetworkTypeEnum(this.value);

  static NetworkTypeEnum? getEnumByValue(int? value) {
    if (value == null) return NetworkTypeEnum.notRequired;
    return NetworkTypeEnum.values.firstWhere(
      (network) => network.value == value,
      orElse: () => NetworkTypeEnum.notRequired,
    );
  }

  int rawValue() => value;
}

extension NetworkTypeExtension on NetworkTypeEnum {
  static NetworkTypeEnum fromRawValue(int rawValue) {
    return NetworkTypeEnum.getEnumByValue(rawValue) ??
        NetworkTypeEnum.notRequired;
  }
}
