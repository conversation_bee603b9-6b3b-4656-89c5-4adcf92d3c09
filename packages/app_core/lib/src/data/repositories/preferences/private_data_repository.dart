import '../database/entities/call_log_private_data.dart';
import '../database/entities/channel_private_data.dart';
import '../database/entities/private_data.dart';
import '../database/entities/user_private_data.dart';

abstract class PrivateDataRepository {
  int insert(PrivateData privateData);

  PrivateData? getPrivateData();

  bool delete();

  List<ChannelPrivateData> getChannels();

  List<UserPrivateData> getUsers();

  List<UserPrivateData>? getUsersByListUserId(List<String> userId);

  List<CallLogPrivateData> getCallLogs();

  Stream<List<ChannelPrivateData>> getStreamChannels();

  Stream<List<UserPrivateData>> getStreamUsers();

  Stream<List<CallLogPrivateData>> getStreamCallLogs();

  ChannelPrivateData? getChannel(String channelId);

  UserPrivateData? getUser(String userId);

  CallLogPrivateData? getCallLog(String callId);

  Stream<ChannelPrivateData?> getStreamChannel(String channelId);

  Stream<UserPrivateData?> getStreamUser(String userId);

  Stream<CallLogPrivateData?> getStreamCallLog(String callId);

  int insertChannel(ChannelPrivateData channel);

  int insertUser(UserPrivateData user);

  int upsert(UserPrivateData user);

  int insertCallLog(CallLogPrivateData callLog);

  bool deleteChannel(String channelId);

  bool deleteUser(String userId);

  bool deleteCallLog(String callLogId);

  int updateChannel(ChannelPrivateData channel);

  int updateUser(UserPrivateData user);

  int updateCallLog(CallLogPrivateData callLog);

  int? getMaxSortChannel();

  void deleteSession(String sessionKey);

  List<UserPrivateData> getUsersByKeyword(String keyword);
}
