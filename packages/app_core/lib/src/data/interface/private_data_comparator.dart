import 'package:diffutil_dart/diffutil.dart';

import '../../data/interface/base_private_data.dart';

class PrivateDataComparator {
  void processUpdates<T extends BasePrivateData>({
    required List<T> oldList,
    required List<T> newList,
    required void Function(T) onInsert,
    required void Function(T) onUpdate,
    required void Function(String) onDelete,
  }) {
    final diffResult = calculateListDiff<T>(
      oldList,
      newList,
      equalityChecker: (a, b) =>
          a.objectId == b.objectId && a.objectVersion == b.objectVersion,
    );

    diffResult.getUpdatesWithData().forEach((update) {
      update.when(
        insert: (index, newItem) => onInsert(newItem),
        remove: (index, oldItem) => onDelete(oldItem.objectId),
        change: (index, oldItem, newItem) {
          if (newItem.objectVersion > oldItem.objectVersion) {
            onUpdate(newItem);
          }
        },
        move: (fromIndex, toIndex, newItem) {},
      );
    });
  }
}
