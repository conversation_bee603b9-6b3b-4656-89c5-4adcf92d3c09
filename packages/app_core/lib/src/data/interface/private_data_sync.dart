import 'package:json_annotation/json_annotation.dart';

part 'private_data_sync.g.dart';

@JsonSerializable(explicitToJson: true)
class PrivateDataSync {
  final String key;
  final Map<String, dynamic> value;

  PrivateDataSync({
    required this.key,
    required this.value,
  });

  factory PrivateDataSync.fromJson(Map<String, dynamic> json) =>
      _$PrivateDataSyncFromJson(json);

  Map<String, dynamic> toJson() => _$PrivateDataSyncToJson(this);
}
