# Message Sending Flow

Tài liệu này mô tả chi tiết luồng gửi các loại tin nhắn khác nhau trong ứng dụng, từ khi người dùng nhấn nút gửi cho đến khi tin nhắn được gửi thành công hoặc thất bại.

## Tổng quan về Kiến trúc

Ứng dụng sử dụng kiến trúc isolate để xử lý các tác vụ gửi tin nhắn, tải lên tệp và nén phương tiện. Kiến trúc này giúp tránh việc chặn UI thread và cải thiện hiệu suất tổng thể của ứng dụng.

### ResilientIsolate

ResilientIsolate là một lớp wrapper quản lý việc tạo và giao tiếp với worker isolate. <PERSON>ó cung cấp các tính năng sau:

- **<PERSON>u<PERSON>n lý hàng đợi tác vụ**: S<PERSON><PERSON> xế<PERSON> các tác vụ theo độ ưu tiên (cao, trung bình, thấp)
- **Xử lý lỗi**: Tự động khởi động lại isolate khi gặp sự cố
- **Giám sát sức khỏe**: Kiểm tra định kỳ trạng thái của isolate
- **Khôi phục tác vụ**: Khôi phục các tác vụ đang chạy khi isolate bị khởi động lại
- **Xử lý lỗi mạng**: Tự động thử lại các tác vụ khi kết nối mạng được khôi phục

### WorkerIsolate

WorkerIsolate là isolate thực tế thực hiện các tác vụ. Nó xử lý:

- Nén hình ảnh và video
- Tải lên tệp
- Gửi tin nhắn đến API
- Xử lý lỗi và phản hồi

### IsolateTaskService

IsolateTaskService là lớp trung gian giữa UI và ResilientIsolate. Nó cung cấp API để đăng ký tác vụ và nhận kết quả.

## 1. Text Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant SMH as SendMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant TMH as TextMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Press send button
    UI->>SMH: sendTextMessage(message)
    SMH->>TMF: createTextMessage()
    TMF-->>SMH: tempMessage
    SMH->>DB: _saveTempMessage(tempMessage)
    SMH->>SMH: _registerTaskOnNextEventLoop()
    SMH->>ITS: registerTask(WorkerSendTextInput)
    SMH->>SML: _listenSentMessageResponse(tempMessage)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>TMH: handleTextMessage()
    TMH->>TMH: Check for timeout
    TMH->>API: POST /Message/SendMessage or /Message/SendDMMessage

    alt Success
        API-->>TMH: Success response
        TMH->>WI: handleSuccess()
        WI->>SML: Send success response via SendPort
        SML->>DB: Update message status to SUCCESS
        SML->>UI: Notify UI via callback or AppEventBus
    else Failure
        API-->>TMH: Error response
        TMH->>WI: handleError()
        WI->>SML: Send failure response via SendPort
        SML->>DB: Update message status to FAILURE
        SML->>UI: Notify UI via callback or AppEventBus
    end
```

### Khởi tạo và Gửi tin nhắn
1. **Người dùng nhấn nút gửi**
   - Bắt đầu từ lớp UI (thường là trong `channel_view_page.dart`)
   - Gọi `SendMessageHandler.sendTextMessage()`

2. **Tạo tin nhắn tạm thời (`SendMessageHandler.sendTextMessage()`)**
   - Tạo tin nhắn tạm thời sử dụng `TempMessageFactory.createTextMessage()`
   - Thiết lập các thuộc tính cơ bản: workspaceId, channelId, userId, content
   - Đặt trạng thái tin nhắn thành `MessageStatus.PENDING`
   - Gọi `_saveTempMessage()` để lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Gọi `_addTempMessage()` để hiển thị tin nhắn trên UI ngay lập tức

3. **Đăng ký tác vụ với IsolateTaskService**
   - Gọi `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Gọi `IsolateTaskService().registerTask()`
   - Tạo `WorkerSendTextInput` với các thông tin cần thiết
   - Đặt `taskName` thành `TaskNameEnum.sendMessage.value`
   - Đặt `attachmentType` thành `AttachmentType.UNSPECIFIED.rawValue()`
   - Đặt `networkRequired` thành `true` để cho phép tự động thử lại khi mạng được khôi phục

4. **Lắng nghe phản hồi**
   - Gọi `_listenSentMessageResponse()` để lắng nghe kết quả gửi tin nhắn
   - Tạo một `ReceivePort` và đăng ký với `IsolateNameServer`
   - Thiết lập callback để xử lý khi nhận được phản hồi

### Xử lý trong Worker Isolate
1. **Worker Isolate nhận tác vụ**
   - `IsolateTaskService` chuyển tác vụ đến `ResilientIsolate`
   - `ResilientIsolate` đưa tác vụ vào hàng đợi theo độ ưu tiên
   - `ResilientIsolate` gọi `_processQueue()` để xử lý hàng đợi tác vụ
   - `ResilientIsolate` gửi tác vụ đến `WorkerIsolate` thông qua `SendPort`
   - `WorkerIsolate.executeTask()` được gọi với thông tin tác vụ

2. **Xử lý tác vụ tin nhắn văn bản**
   - Kiểm tra loại tác vụ là `sendMessage` và loại đính kèm là `UNSPECIFIED`
   - Gọi `TextMessageHandler.handleTextMessage()`
   - Kiểm tra thời gian chờ trước khi gửi yêu cầu API
   - Gọi API endpoint `/Message/SendMessage` hoặc `/Message/SendDMMessage` tùy thuộc vào loại trò chuyện

3. **Xử lý kết quả API**
   - Nếu thành công: Gọi `WorkerSendResultHandler.handleSuccess()`
   - Nếu thất bại: Gọi `WorkerSendResultHandler.handleError()`
   - Lưu kết quả vào cơ sở dữ liệu thông qua `_saveSendMessageResult()`

### Xử lý phản hồi
1. **Gửi phản hồi đến Main Isolate**
   - Tìm `SendPort` đã đăng ký thông qua `IsolateNameServer.lookupPortByName()`
   - Gửi kết quả đến main isolate thông qua `sendPort.send()`

2. **Main Isolate nhận phản hồi**
   - `SendMessageListener._handleIncomingEvent()` xử lý sự kiện nhận được
   - Cập nhật trạng thái tin nhắn trong cơ sở dữ liệu
   - Thông báo cho UI thông qua callback hoặc `AppEventBus.publish()`

3. **Cập nhật UI**
   - UI nhận sự kiện thông qua callback hoặc `AppEventBus`
   - Cập nhật hiển thị tin nhắn với trạng thái mới

## 2. Media Message Flow (Image/Video)

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant SIMH as SendImageMessageHandler
    participant SVMH as SendVideoMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant CH as CompressHandler
    participant UFH as UploadFileHandler
    participant MMH as MediaMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Select media and press send

    alt Image Message
        UI->>SIMH: sendImageMessage(imageList)
        SIMH->>TMF: createBaseMessage(messageViewType: imagesOwner)
    else Video Message
        UI->>SVMH: sendVideoMessage(videoList)
        SVMH->>TMF: createBaseMessage(messageViewType: videoOwner)
    end

    TMF-->>UI: tempMessage
    UI->>DB: _saveTempMessage(tempMessage)

    alt Sequential Upload
        UI->>UI: _uploadSequentially()
    else Parallel Upload
        UI->>UI: _uploadInParallelBatches()
    end

    alt Video Needs Compression
        UI->>UI: _registerTaskOnNextEventLoop()
        UI->>ITS: registerTask(WorkerCompressVideoInput)
        ITS->>RI: Add task to queue
        RI->>WI: executeTask()
        WI->>CH: handleCompressVideo()
        CH-->>WI: compressedVideo
    end

    UI->>UI: _registerTaskOnNextEventLoop()
    UI->>ITS: registerTask(WorkerUploadFileInput)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>UFH: handleUploadFile()
    UFH->>API: Upload file to server
    API-->>UFH: File URL

    alt First File Upload
        UFH->>WI: _registerTaskWithResilientIsolate()
        WI->>ITS: registerTask(WorkerSendMediaInput)
        ITS->>RI: Add task to queue
        RI->>WI: executeTask()
        WI->>MMH: handleMediaMessage()
        MMH->>API: POST /Message/SendMediaMessage
        API-->>MMH: Response
        MMH->>WI: handleSuccess() or handleError()
        WI->>SML: Send response via SendPort
        SML->>DB: Update message status
        SML->>UI: Notify UI
    else Additional File Upload
        UFH->>WI: _registerTaskWithResilientIsolate()
        WI->>ITS: registerTask(WorkerUpdateMediaAttachmentsInput)
        ITS->>RI: Add task to queue
        RI->>WI: executeTask()
        WI->>MMH: handleUpdateMediaAttachments()
        MMH->>API: POST /Message/UpdateMediaAttachments
        API-->>MMH: Response
        MMH->>WI: handleSuccess() or handleError()
        WI->>SML: Send response via SendPort
        SML->>DB: Update attachment status
        SML->>UI: Notify UI
    end
```

### Khởi tạo và Gửi tin nhắn phương tiện
1. **Người dùng chọn phương tiện và nhấn gửi**
   - Bắt đầu từ lớp UI
   - Gọi `SendImageMessageHandler.sendImageMessage()` hoặc `SendVideoMessageHandler.sendVideoMessage()`

2. **Tạo tin nhắn tạm thời**
   - Tạo tin nhắn tạm thời với `TempMessageFactory.createBaseMessage()`
   - Đặt `messageViewType` thành `MessageViewType.imagesOwner` hoặc `MessageViewType.videoOwner`
   - Đặt `attachmentType` phù hợp (IMAGE hoặc VIDEO)
   - Lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Hiển thị tin nhắn trên UI ngay lập tức với trạng thái PENDING

3. **Xử lý tải lên tệp**
   - Hai phương pháp tải lên: tuần tự (`_uploadSequentially`) hoặc song song (`_uploadInParallelBatches`)
   - Mỗi tệp được xử lý bởi `UploadImageHandler` hoặc `UploadVideoHandler`
   - Hình ảnh có thể được tải lên theo lô (batches) để tối ưu hiệu suất

4. **Đăng ký tác vụ nén và tải lên**
   - Đối với video: Đăng ký tác vụ `compressVideoMessage` trước
   - Đối với hình ảnh: Có thể nén trước hoặc tải lên trực tiếp
   - Sử dụng `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Tạo `WorkerCompressAndUploadImagesInput` hoặc `WorkerCompressVideoInput`
   - Gọi `IsolateTaskService().registerTask()` với thông tin tác vụ
   - Đặt `networkRequired` thành `true` để cho phép tự động thử lại khi mạng được khôi phục

### Xử lý trong Worker Isolate
1. **Nén phương tiện (nếu cần)**
   - Video: `VideoCompressHandler.handleCompressVideo()` sử dụng thư viện nén video
   - Hình ảnh: `ImageCompressHandler.handleCompressImage()` giảm kích thước và chất lượng
   - Lưu tệp đã nén vào bộ nhớ tạm thời

2. **Tải lên tệp**
   - Gọi `UploadFileHandler.handleUploadFile()`
   - Tải tệp lên máy chủ và nhận URL
   - Cập nhật trạng thái tải lên cho từng tệp
   - Gửi sự kiện `AttachmentStatusUpdatedEvent` đến main isolate

3. **Gửi tin nhắn với phương tiện**
   - Sau khi tải lên thành công, đăng ký tác vụ `sendMessage` sử dụng `_registerTaskWithResilientIsolate()`
   - Worker isolate gọi `IsolateTaskService().registerTask()` với thông tin tác vụ
   - Tạo `WorkerSendMediaInput` với thông tin phương tiện đã tải lên
   - Gọi API endpoint `/Message/SendMediaMessage` hoặc `/Message/SendDMMediaMessage`
   - Xử lý kết quả API và gửi phản hồi đến main isolate

### Xử lý phản hồi
1. **Cập nhật trạng thái tải lên**
   - Mỗi tệp tải lên cập nhật trạng thái riêng của nó
   - Gửi `AttachmentStatusUpdatedEvent` đến main isolate
   - UI hiển thị tiến trình tải lên cho từng tệp

2. **Cập nhật trạng thái tin nhắn**
   - Khi tất cả các tệp được tải lên và tin nhắn được gửi thành công
   - Cập nhật tin nhắn với thông tin từ máy chủ
   - Thông báo cho UI thông qua callback hoặc `AppEventBus`
   - UI cập nhật hiển thị tin nhắn với trạng thái mới

## 3. Voice Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer (SendZiiVoiceBottomSheetWidget)
    participant SVMH as SendVoiceMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant UFH as UploadFileHandler
    participant MMH as MediaMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Record and press send
    UI->>SVMH: sendVoiceMessage(voiceFile)
    SVMH->>TMF: createBaseMessage(messageViewType: ziiVoiceOwner)
    TMF-->>SVMH: tempMessage
    SVMH->>DB: _saveTempMessage(tempMessage)
    SVMH->>SVMH: _uploadSequentially(voiceFile)
    SVMH->>SVMH: _registerTaskOnNextEventLoop()
    SVMH->>ITS: registerTask(WorkerUploadFileInput)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>UFH: handleUploadFile()
    UFH->>API: Upload voice file to server
    API-->>UFH: File URL
    UFH->>WI: _registerTaskWithResilientIsolate()
    WI->>ITS: registerTask(WorkerSendMediaInput)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>MMH: handleMediaMessage()
    MMH->>API: POST /Message/SendMediaMessage

    alt Success
        API-->>MMH: Success response
        MMH->>WI: handleSuccess()
        WI->>SML: Send success response via SendPort
        SML->>DB: Update message status to SUCCESS
        SML->>UI: Notify UI via callback or AppEventBus
    else Failure
        API-->>MMH: Error response
        MMH->>WI: handleError()
        WI->>SML: Send failure response via SendPort
        SML->>DB: Update message status to FAILURE
        SML->>UI: Notify UI via callback or AppEventBus
    end
```

### Khởi tạo và Gửi tin nhắn âm thanh
1. **Người dùng ghi âm và nhấn gửi**
   - Bắt đầu từ `SendZiiVoiceBottomSheetWidget`
   - Gọi `SendVoiceMessageHandler.sendVoiceMessage()`

2. **Tạo tin nhắn tạm thời**
   - Tạo tin nhắn tạm thời với `TempMessageFactory.createBaseMessage()`
   - Đặt `messageViewType` thành `MessageViewType.ziiVoiceOwner`
   - Đặt `attachmentType` thành `AttachmentType.VOICE_MESSAGE`
   - Lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Hiển thị tin nhắn trên UI ngay lập tức với trạng thái PENDING

3. **Tải lên tệp âm thanh**
   - Xử lý tải lên tuần tự với `_uploadSequentially()`
   - Sử dụng `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Đăng ký tác vụ `uploadFile` với `IsolateTaskService`
   - Tạo `WorkerUploadFileInput` với thông tin tệp âm thanh
   - Đặt `uploadType` thành `UploadFileTypeEnum.voice.value`

### Xử lý trong Worker Isolate
1. **Tải lên tệp âm thanh**
   - Gọi `UploadFileHandler.handleUploadFile()`
   - Tải tệp lên máy chủ và nhận URL
   - Cập nhật trạng thái tải lên
   - Gửi sự kiện `AttachmentStatusUpdatedEvent` đến main isolate

2. **Gửi tin nhắn với âm thanh**
   - Sau khi tải lên thành công, worker isolate gọi `_registerTaskWithResilientIsolate()`
   - Worker isolate đăng ký tác vụ `sendMessage` với `IsolateTaskService`
   - Tạo `WorkerSendMediaInput` với thông tin âm thanh đã tải lên
   - Gọi API endpoint `/Message/SendMediaMessage` hoặc `/Message/SendDMMediaMessage`
   - Xử lý kết quả API và gửi phản hồi đến main isolate

### Xử lý phản hồi
1. **Cập nhật trạng thái tải lên**
   - Gửi `AttachmentStatusUpdatedEvent` đến main isolate
   - UI hiển thị tiến trình tải lên

2. **Cập nhật trạng thái tin nhắn**
   - Khi tệp âm thanh được tải lên và tin nhắn được gửi thành công
   - Cập nhật tin nhắn với thông tin từ máy chủ
   - Thông báo cho UI thông qua callback hoặc `AppEventBus`
   - UI cập nhật hiển thị tin nhắn với trạng thái mới

## 4. Sticker Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant SMH as SendMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant SMH as StickerMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Select sticker and press send
    UI->>SMH: sendStickerMessage(sticker)
    SMH->>TMF: createStickerMessage()
    TMF-->>SMH: tempMessage
    SMH->>DB: _saveTempMessage(tempMessage)
    SMH->>SMH: _registerTaskOnNextEventLoop()
    SMH->>ITS: registerTask(WorkerSendStickerInput)
    SMH->>SML: _listenSentMessageResponse(tempMessage)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>SMH: handleStickerMessage()
    SMH->>API: POST /Message/SendMessageSticker or /Message/SendDMMessageSticker

    alt Success
        API-->>SMH: Success response
        SMH->>WI: handleSuccess()
        WI->>SML: Send success response via SendPort
        SML->>DB: Update message status to SUCCESS
        SML->>UI: Notify UI via callback or AppEventBus
    else Failure
        API-->>SMH: Error response
        SMH->>WI: handleError()
        WI->>SML: Send failure response via SendPort
        SML->>DB: Update message status to FAILURE
        SML->>UI: Notify UI via callback or AppEventBus
    end
```

### Khởi tạo và Gửi tin nhắn sticker
1. **Người dùng chọn sticker và nhấn gửi**
   - Bắt đầu từ lớp UI
   - Gọi `SendMessageHandler.sendStickerMessage()` hoặc `SendMessageHandler.sendPokeMessage()` cho sticker poke

2. **Tạo tin nhắn tạm thời**
   - Tạo tin nhắn tạm thời với `TempMessageFactory.createStickerMessage()` hoặc `TempMessageFactory.createPokeMessage()`
   - Đặt `messageViewType` thành `MessageViewType.stickerOwner`
   - Đặt `attachmentType` thành `AttachmentType.STICKER`
   - Lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Hiển thị tin nhắn trên UI ngay lập tức với trạng thái PENDING

3. **Đăng ký tác vụ với IsolateTaskService**
   - Gọi `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Gọi `IsolateTaskService().registerTask()`
   - Tạo `WorkerSendStickerInput` với thông tin sticker
   - Đặt `taskName` thành `TaskNameEnum.sendMessage.value`
   - Đặt `networkRequired` thành `true` để cho phép tự động thử lại khi mạng được khôi phục
   - Đặt `isPoked` thành `true` nếu là sticker poke

### Xử lý trong Worker Isolate
1. **Gửi tin nhắn sticker**
   - Gọi `StickerMessageHandler.handleStickerMessage()`
   - Gọi API endpoint `/Message/SendMessageSticker` hoặc `/Message/SendDMMessageSticker`
   - Xử lý kết quả API và gửi phản hồi đến main isolate

### Xử lý phản hồi
1. **Main Isolate nhận phản hồi**
   - `SendMessageListener._handleIncomingEvent()` xử lý sự kiện nhận được
   - Cập nhật trạng thái tin nhắn trong cơ sở dữ liệu
   - Thông báo cho UI thông qua callback hoặc `AppEventBus.publish()`

2. **Cập nhật UI**
   - UI nhận sự kiện thông qua callback hoặc `AppEventBus`
   - Cập nhật hiển thị tin nhắn với trạng thái mới

## 5. Location Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant SMH as SendMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant LMH as LocationMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Select location and press send
    UI->>SMH: sendLocationMessage(latitude, longitude, address)
    SMH->>TMF: createLocationMessage()
    TMF-->>SMH: tempMessage
    SMH->>DB: _saveTempMessage(tempMessage)
    SMH->>SMH: _registerTaskOnNextEventLoop()
    SMH->>ITS: registerTask(WorkerSendLocationInput)
    SMH->>SML: _listenSentMessageResponse(tempMessage)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>LMH: handleLocationMessage()
    LMH->>API: POST /Message/SendLocationMessage or /Message/SendDMLocationMessage

    alt Success
        API-->>LMH: Success response
        LMH->>WI: handleSuccess()
        WI->>SML: Send success response via SendPort
        SML->>DB: Update message status to SUCCESS
        SML->>UI: Notify UI via callback or AppEventBus
    else Failure
        API-->>LMH: Error response
        LMH->>WI: handleError()
        WI->>SML: Send failure response via SendPort
        SML->>DB: Update message status to FAILURE
        SML->>UI: Notify UI via callback or AppEventBus
    end
```

### Khởi tạo và Gửi tin nhắn vị trí
1. **Người dùng chọn vị trí và nhấn gửi**
   - Bắt đầu từ lớp UI
   - Gọi `SendMessageHandler.sendLocationMessage()`

2. **Tạo tin nhắn tạm thời**
   - Tạo tin nhắn tạm thời với `TempMessageFactory.createLocationMessage()`
   - Đặt `messageViewType` thành `MessageViewType.locationOwner`
   - Đặt `attachmentType` thành `AttachmentType.LOCATION`
   - Lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Hiển thị tin nhắn trên UI ngay lập tức với trạng thái PENDING

3. **Đăng ký tác vụ với IsolateTaskService**
   - Gọi `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Gọi `IsolateTaskService().registerTask()`
   - Tạo `WorkerSendLocationInput` với thông tin vị trí (kinh độ, vĩ độ, địa chỉ)
   - Đặt `taskName` thành `TaskNameEnum.sendMessage.value`
   - Đặt `networkRequired` thành `true` để cho phép tự động thử lại khi mạng được khôi phục

### Xử lý trong Worker Isolate
1. **Gửi tin nhắn vị trí**
   - Gọi `LocationMessageHandler.handleLocationMessage()`
   - Gọi API endpoint `/Message/SendLocationMessage` hoặc `/Message/SendDMLocationMessage`
   - Xử lý kết quả API và gửi phản hồi đến main isolate

### Xử lý phản hồi
1. **Main Isolate nhận phản hồi**
   - `SendMessageListener._handleIncomingEvent()` xử lý sự kiện nhận được
   - Cập nhật trạng thái tin nhắn trong cơ sở dữ liệu
   - Thông báo cho UI thông qua callback hoặc `AppEventBus.publish()`

2. **Cập nhật UI**
   - UI nhận sự kiện thông qua callback hoặc `AppEventBus`
   - Cập nhật hiển thị tin nhắn với trạng thái mới

## 6. File Message Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Layer
    participant SFMH as SendFileMessageHandler
    participant TMF as TempMessageFactory
    participant ITS as IsolateTaskService
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant UFH as UploadFileHandler
    participant MMH as MediaMessageHandler
    participant API as API Server
    participant DB as Database

    User->>UI: Select file and press send
    UI->>SFMH: sendFileMessage(fileList)
    SFMH->>TMF: createBaseMessage(messageViewType: fileOwner)
    TMF-->>SFMH: tempMessage
    SFMH->>DB: _saveTempMessage(tempMessage)
    SFMH->>SFMH: _uploadSequentially(fileList)
    SFMH->>SFMH: _registerTaskOnNextEventLoop()
    SFMH->>ITS: registerTask(WorkerUploadFileInput)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>UFH: handleUploadFile()
    UFH->>API: Upload file to server
    API-->>UFH: File URL
    UFH->>WI: _registerTaskWithResilientIsolate()
    WI->>ITS: registerTask(WorkerSendMediaInput)
    ITS->>RI: Add task to queue
    RI->>WI: executeTask()
    WI->>MMH: handleMediaMessage()
    MMH->>API: POST /Message/SendMediaMessage

    alt Success
        API-->>MMH: Success response
        MMH->>WI: handleSuccess()
        WI->>SML: Send success response via SendPort
        SML->>DB: Update message status to SUCCESS
        SML->>UI: Notify UI via callback or AppEventBus
    else Failure
        API-->>MMH: Error response
        MMH->>WI: handleError()
        WI->>SML: Send failure response via SendPort
        SML->>DB: Update message status to FAILURE
        SML->>UI: Notify UI via callback or AppEventBus
    end
```

### Khởi tạo và Gửi tin nhắn tệp
1. **Người dùng chọn tệp và nhấn gửi**
   - Bắt đầu từ lớp UI
   - Gọi `SendFileMessageHandler.sendFileMessage()`

2. **Tạo tin nhắn tạm thời**
   - Tạo tin nhắn tạm thời với `TempMessageFactory.createBaseMessage()`
   - Đặt `messageViewType` thành `MessageViewType.fileOwner`
   - Đặt `attachmentType` thành `AttachmentType.FILE`
   - Lưu tin nhắn tạm thời vào cơ sở dữ liệu
   - Hiển thị tin nhắn trên UI ngay lập tức với trạng thái PENDING

3. **Tải lên tệp**
   - Xử lý tải lên tuần tự với `_uploadSequentially()`
   - Sử dụng `_registerTaskOnNextEventLoop()` để đăng ký tác vụ trên event loop tiếp theo
   - Đăng ký tác vụ `uploadFile` với `IsolateTaskService`
   - Tạo `WorkerUploadFileInput` với thông tin tệp
   - Đặt `uploadType` thành `UploadFileTypeEnum.file.value`
   - Đặt `networkRequired` thành `true` để cho phép tự động thử lại khi mạng được khôi phục

### Xử lý trong Worker Isolate
1. **Tải lên tệp**
   - Gọi `UploadFileHandler.handleUploadFile()`
   - Tải tệp lên máy chủ và nhận URL
   - Cập nhật trạng thái tải lên
   - Gửi sự kiện `AttachmentStatusUpdatedEvent` đến main isolate

2. **Gửi tin nhắn với tệp**
   - Sau khi tải lên thành công, worker isolate gọi `_registerTaskWithResilientIsolate()`
   - Worker isolate đăng ký tác vụ `sendMessage` với `IsolateTaskService`
   - Tạo `WorkerSendMediaInput` với thông tin tệp đã tải lên
   - Gọi API endpoint `/Message/SendMediaMessage` hoặc `/Message/SendDMMediaMessage`
   - Xử lý kết quả API và gửi phản hồi đến main isolate

### Xử lý phản hồi
1. **Cập nhật trạng thái tải lên**
   - Gửi `AttachmentStatusUpdatedEvent` đến main isolate
   - UI hiển thị tiến trình tải lên

2. **Cập nhật trạng thái tin nhắn**
   - Khi tệp được tải lên và tin nhắn được gửi thành công
   - Cập nhật tin nhắn với thông tin từ máy chủ
   - Thông báo cho UI thông qua callback hoặc `AppEventBus`
   - UI cập nhật hiển thị tin nhắn với trạng thái mới

## 7. Error Handling Flow

```mermaid
flowchart TD
    A[Error Occurs] --> B{Error Type}

    B -->|Timeout| C[SendMessageTimeoutException]
    B -->|User Blocked| D[isBlockedException]
    B -->|Message Limit| E[hasReachedMessageLimit]
    B -->|Network| F[isNetworkError]
    B -->|Server| G[isNoHealthyUpstreamError/hasServerErrorHandling]
    B -->|File Not Found| H[FileNotFoundException]
    B -->|Other| I[Unknown Error]

    C --> J[WorkerSendResultHandler.handleError]
    D --> J
    E --> J
    F --> Z[Silent Network Error Handling]
    G --> J
    H --> J
    I --> J

    J --> K[Send Error Response to Main Isolate]
    K --> L[SendMessageListener._handleIncomingEvent]
    L --> M[Update Message Status to FAILURE]
    M --> N[Set messageErrorReason]
    N --> O[Notify UI]

    Z --> Z1[Keep Message in PENDING State]
    Z1 --> Z2[Queue Task for Retry]
    Z2 --> Z3[Monitor Network Connectivity]
    Z3 --> Z4{Network Restored?}
    Z4 -->|No| Z3
    Z4 -->|Yes| Z5[Retry Task Automatically]
    Z5 --> W[Start Send Flow Again]

    O --> P{Error Type for UI}
    P -->|Blocked User| Q[Show System Message: "This person isn't receiving messages right now"]
    P -->|Message Limit| R[Show System Message: "You have reached the maximum message limit for strangers"]
    P -->|Other Errors| S[Show Error Icon]

    Q --> T[Allow Manual Retry]
    R --> T
    S --> T

    T --> U[User Taps Error Message]
    U --> V[Resend Message]
    V --> W
```

### Error Types
1. **Timeout Error**
   - Occurs when message sending request exceeds timeout
   - Handled by `SendMessageTimeoutException`
   - Error code: `SendMsgErrorEnum.timeout`

2. **User Blocked Error**
   - Occurs when recipient has blocked sender
   - Handled by `isBlockedException`
   - Error code: `SendMsgErrorEnum.blockedUser`
   - Shows system message: "This person isn't receiving messages right now"

3. **Message Limit Error**
   - Occurs when sender has reached maximum message limit
   - Handled by `hasReachedMessageLimit`
   - Error code: `SendMsgErrorEnum.reachedMessageLimit`
   - Shows system message: "You have reached the maximum message limit for strangers"

4. **Network Error**
   - Occurs when there is no network connection or unstable connection
   - Handled by `isNetworkError` in `DioException`
   - Error code: `SendMsgErrorEnum.network`
   - Message remains in `PENDING` state instead of showing error
   - Task is automatically retried when network connectivity is restored

5. **Server Error**
   - Occurs when server is not responding or has an error
   - Handled by `isNoHealthyUpstreamError` or `hasServerErrorHandling`
   - Error code: `SendMsgErrorEnum.serverUnavailable` or `SendMsgErrorEnum.serverError`

6. **File Not Found Error**
   - Occurs when file to upload does not exist
   - Handled by `FileNotFoundException`
   - Error code: `SendMsgErrorEnum.fileNotFound`

7. **Unknown Error**
   - Other errors not in above categories
   - Error code: `SendMsgErrorEnum.unknown`

### Error Handling Mechanism
1. **In Worker Isolate**
   - `WorkerSendResultHandler.handleError()` processes different error types
   - Sends error notification to main isolate via `sendPort.send()`
   - Saves error state to database via `_saveSendMessageResult()`

2. **In Main Isolate**
   - `SendMessageListener._handleIncomingEvent()` processes error event
   - Updates message status to `MessageStatus.FAILURE`
   - Sets `messageErrorReason` corresponding to error type
   - Notifies UI via callback or `AppEventBus`

3. **Display Error on UI**
   - Shows error icon next to message
   - Shows system message for specific error types
   - Allows user to retry by tapping on error message

## 8. Network Error Handling

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant SML as SendMessageListener
    participant RI as ResilientIsolate
    participant WI as WorkerIsolate
    participant API as API Server
    participant NM as NetworkManager

    UI->>RI: Register task with networkRequired=true
    RI->>WI: Execute task
    WI->>API: Send API request
    API-->>WI: Network error (DioException)

    WI->>WI: Detect network error (isNetworkError)
    WI->>WI: Set success=false but don't set error reason
    WI->>RI: Return result with isNetworkError=true

    RI->>RI: Check if network-related error
    RI->>RI: Keep task in PENDING state
    RI->>RI: Add to retry queue
    RI->>NM: Listen for network connectivity changes

    NM-->>RI: Network connectivity restored
    RI->>RI: Get tasks from retry queue
    RI->>RI: Set high priority
    RI->>WI: Retry tasks
    WI->>API: Retry API request
    API-->>WI: Success response
    WI->>RI: Return success result
    RI->>SML: Send success response
    SML->>UI: Update UI with success status
```

### Network Error Detection
1. **In Worker Isolate**
   - `DioException` with `isNetworkError` property is caught
   - Network errors are handled silently without showing error to user
   - Task result includes `isNetworkError: true` flag

2. **In IsolateTaskService and ResilientIsolate**
   - `IsolateTaskService` forwards task to `ResilientIsolate`
   - `ResilientIsolate` checks for network-related errors in task result
   - Keeps message in `PENDING` state instead of `FAILURE`
   - Adds task to network-pending queue for retry

### Network Monitoring
1. **NetworkManager**
   - Monitors network connectivity changes
   - Notifies listeners when network is restored

2. **Automatic Retry**
   - When network is restored, tasks in network-pending queue are retried
   - Tasks are given high priority to ensure quick processing
   - No user intervention required

## 9. Retry Mechanism

### Automatic Retry
1. **Retry When Isolate Restarts**
   - When isolate is unresponsive and restarts
   - `ResilientIsolate._resetRunningTasks()` moves running tasks to pending state
   - Sends `SendMessageRetryResponse` to main isolate

2. **Retry When Network Recovers**
   - Tasks with network errors are silently queued for retry
   - `ResilientIsolate` detects network-related errors and handles them specially
   - Messages remain in `PENDING` state instead of showing error
   - When network connectivity is restored, tasks are automatically retried
   - Handled by `networkRequired` flag in `TaskModel` and network monitoring in `ResilientIsolate`

### Manual Retry
1. **User Taps Error Message**
   - UI shows "Retry" option when tapping error message
   - Calls corresponding message sending function again
   - Passes error message to handle as a resend flow

2. **Resend Flow Handling**
   - Updates creation and update time of message
   - Keeps same ref as old message
   - Uses `_registerTaskOnNextEventLoop()` to register task on next event loop
   - Registers task again with `IsolateTaskService`

## 10. General Processing Flow

### Initialization and Sending
1. **Create Temporary Message**
   - Create temporary message with `PENDING` status
   - Save to database to display immediately on UI
   - Create unique reference (ref) for message

2. **Register Task**
   - Use `_registerTaskOnNextEventLoop()` to register task on next event loop
   - Register task with `IsolateTaskService`
   - Set necessary information for task including `networkRequired` flag
   - Tasks with `networkRequired=true` will be automatically retried when network is restored

3. **Listen for Response**
   - Register listener with `SendMessageListener`
   - Set up callback to handle when response is received

### Task Processing
1. **Worker Isolate Receives Task**
   - `IsolateTaskService` forwards task to `ResilientIsolate`
   - `ResilientIsolate` manages task queue
   - Passes task to `WorkerIsolate` for processing

2. **Process Task by Type**
   - Based on task type and attachment type
   - Call corresponding handler to process

3. **Send Result to Main Isolate**
   - Find registered `SendPort`
   - Send result to main isolate

### Response Handling
1. **Main Isolate Receives Response**
   - `SendMessageListener` processes event
   - Updates message status

2. **Update UI**
   - Notify UI via callback or `AppEventBus`
   - UI updates message display

### Error Handling
1. **Detect Error**
   - In worker isolate or main isolate
   - Classify error and handle accordingly
   - Network errors are handled silently without showing error to user

2. **Notify Error**
   - Send error notification to main isolate
   - Update message status to `FAILURE`
   - Display error message on UI

3. **Retry if Needed**
   - Automatic retry in some cases
   - Allow user to retry manually
