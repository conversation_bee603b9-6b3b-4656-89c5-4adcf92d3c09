import '../../../../../../shared.dart';

class OnUpdateAttachmentEvent extends LocalEvent {
  OnUpdateAttachmentEvent({
    super.source = BaseEvent.LOCAL_SOURCE,
    required super.data,
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;

  @override
  Map<String, dynamic> get data => super.data as Map<String, dynamic>;
}
