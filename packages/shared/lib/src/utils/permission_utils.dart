import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geocoding/geocoding.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:permission_handler/permission_handler.dart' hide ServiceStatus;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../shared.dart';

class PermissionUtils {
  static StreamSubscription? _streamPosition;

  const PermissionUtils._();

  static Future<bool> goToSettings() => openAppSettings();

  static Future<bool> _requestPermission(
    Permission permission, {
    Future<void> Function()? showRequestRationaleDialog,
  }) async {
    final status = await permission.request();
    if (status == PermissionStatus.granted ||
        status == PermissionStatus.limited) {
      return true;
    }
    await showRequestRationaleDialog?.call();
    return false;
  }

  static Future<void> _showRequestRationaleDialog(
    BuildContext context,
    String title,
    String content,
  ) async {
    final completer = Completer();
    final appLocalizations = AppLocalizations.of(context)!;
    ui.DialogUtils.showAppDialog(
      context: context,
      buildDialogContent: (dialogContext) => ui.AppCustomDialog(
        maxWidth: 350.w,
        title: title,
        content: content,
        firstAction: appLocalizations.notNow,
        onFirstAction: () {
          Navigator.of(dialogContext).pop();
          return completer.complete();
        },
        secondAction: appLocalizations.openSettings,
        onSecondAction: () {
          Navigator.of(dialogContext).pop();
          openAppSettings();
          return completer.complete();
        },
      ),
    );
    return completer.future;
  }

  static Future<bool> isGranted(Permission permission) async {
    final status = await permission.status;
    return status == PermissionStatus.granted ||
        status == PermissionStatus.limited;
  }

  static Future<bool> isGrantedMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status == PermissionStatus.granted ||
        status == PermissionStatus.limited;
  }

  static Future<bool> isGrantedCameraPermission() async {
    final status = await Permission.camera.status;
    return status == PermissionStatus.granted ||
        status == PermissionStatus.limited;
  }

  static Future<bool> requestCameraPermission(
    BuildContext context, {
    VoidCallback? onOpenSetting,
  }) async {
    return _requestPermission(
      Permission.camera,
      showRequestRationaleDialog: () async {
        final completer = Completer();
        ui.DialogUtils.showAccessCameraDialog2(
          context,
          onFirstAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            return completer.complete();
          },
          onSecondAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            openAppSettings();
            onOpenSetting?.call();
            return completer.complete();
          },
        );
        return completer.future;
      },
    );
  }

  static Future<bool> requestMicrophonePermission(
    BuildContext context, {
    VoidCallback? onOpenSetting,
  }) async {
    return _requestPermission(
      Permission.microphone,
      showRequestRationaleDialog: () async {
        final completer = Completer();
        ui.DialogUtils.showAccessMicrophoneRecordDialog2(
          context,
          onFirstAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            return completer.complete();
          },
          onSecondAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            openAppSettings();
            onOpenSetting?.call();
            return completer.complete();
          },
        );
        return completer.future;
      },
    );
  }

  static Future<bool> requestBluetoothPermission(
    BuildContext context, {
    VoidCallback? onOpenSetting,
  }) async {
    return _requestPermission(
      Permission.bluetooth,
      showRequestRationaleDialog: () async {
        final completer = Completer();
        // TODO: Update this dialog to use the new design
        ui.DialogUtils.showAccessMicrophoneRecordDialog2(
          context,
          onFirstAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            return completer.complete();
          },
          onSecondAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            openAppSettings();
            onOpenSetting?.call();
            return completer.complete();
          },
        );
        return completer.future;
      },
    );
  }

  static Future<bool> requestBluetoothConnectPermission(
    BuildContext context, {
    VoidCallback? onOpenSetting,
  }) async {
    return _requestPermission(
      Permission.bluetoothConnect,
      showRequestRationaleDialog: () async {
        final completer = Completer();
        // TODO: Update this dialog to use the new design
        ui.DialogUtils.showAccessMicrophoneRecordDialog2(
          context,
          onFirstAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            return completer.complete();
          },
          onSecondAction: (dialogContext) {
            Navigator.of(dialogContext).pop();
            openAppSettings();
            onOpenSetting?.call();
            return completer.complete();
          },
        );
        return completer.future;
      },
    );
  }

  static Future<bool> requestImagePermission(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    final osVersion = await getDoubleOSVersion();
    Permission permission;
    if (Platform.isAndroid && osVersion < 33) {
      permission = Permission.storage;
    } else {
      permission = Permission.photos;
    }

    return _requestPermission(
      permission,
      showRequestRationaleDialog: () async {
        await _showRequestRationaleDialog(
          context,
          appLocalizations.ziiChatWouldLikeToAccessYourPhotos,
          appLocalizations.pleaseEnableYourPhotosFromTheDeviceSettings,
        );
      },
    );
  }

  static Future<bool> requestVideoPermission(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    final osVersion = await getDoubleOSVersion();
    Permission permission;
    if (Platform.isAndroid) {
      if (osVersion < 33) {
        permission = Permission.storage;
      } else {
        permission = Permission.videos;
      }
    } else {
      permission = Permission.photos;
    }

    return _requestPermission(
      permission,
      showRequestRationaleDialog: () async {
        await _showRequestRationaleDialog(
          context,
          appLocalizations.ziiChatWouldLikeToAccessYourPhotos,
          appLocalizations.pleaseEnableYourPhotosFromTheDeviceSettings,
        );
      },
    );
  }

  static Future<bool> requestSaveToGalleryPermission(
    BuildContext context,
  ) async {
    if (Platform.isAndroid) {
      final osVersion = await getDoubleOSVersion();
      return osVersion >= 29 ? true : await requestImagePermission(context);
    } else {
      return requestImagePermission(context);
    }
  }

  static Future<bool> requestImageAndVideoPermission(
    BuildContext context,
  ) async {
    final appLocalizations = AppLocalizations.of(context)!;
    final permissions = await getRequiredImageAndVideoPermission();

    final listStatus = await permissions.request();
    for (final status in listStatus.values) {
      if (status != PermissionStatus.granted &&
          status != PermissionStatus.limited) {
        await _showRequestRationaleDialog(
          context,
          appLocalizations.ziiChatWouldLikeToAccessYourPhotos,
          appLocalizations.pleaseEnableYourPhotosFromTheDeviceSettings,
        );
        return false;
      }
    }
    return true;
  }

  static Future<List<Permission>> getRequiredImageAndVideoPermission() async {
    final osVersion = await getDoubleOSVersion();
    if (Platform.isAndroid) {
      if (osVersion < 33) {
        return [Permission.storage];
      } else {
        return [Permission.photos, Permission.videos];
      }
    } else {
      return [Permission.photos];
    }
  }

  static Future<bool> isAccessImageAndVideoPermission() async {
    final permissions = await getRequiredImageAndVideoPermission();

    for (final permission in permissions) {
      final status = await permission.status;
      if (status != PermissionStatus.granted &&
          status != PermissionStatus.limited) {
        return false;
      }
    }
    return true;
  }

  static Future<bool> isLimitedAccessImageAndVideoPermission() async {
    final permissions = await getRequiredImageAndVideoPermission();
    for (final permission in permissions) {
      final status = await permission.status;
      if (status == PermissionStatus.limited) {
        return true;
      }
    }
    return false;
  }

  static Future<bool> requestAccessGalleryPermission(
    BuildContext context,
  ) async {
    final osVersion = await getDoubleOSVersion();
    List<Permission> permissions;
    if (Platform.isAndroid) {
      if (osVersion < 33) {
        permissions = [Permission.storage];
      } else {
        permissions = [Permission.photos, Permission.videos];
      }
    } else {
      permissions = [Permission.photos];
    }

    final listStatus = await permissions.request();
    for (final status in listStatus.values) {
      if (status != PermissionStatus.granted &&
          status != PermissionStatus.limited) {
        return false;
      }
    }
    return true;
  }

  static Future<void> showGrantAccessGalleryDialog(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    await _showRequestRationaleDialog(
      context,
      appLocalizations.ziiChatWouldLikeToAccessYourPhotos,
      appLocalizations.pleaseEnableYourPhotosFromTheDeviceSettings,
    );
  }

  static Future<bool> requestAudioPermission(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    final osVersion = await getDoubleOSVersion();
    Permission permission;
    if (Platform.isAndroid) {
      if (osVersion < 33) {
        permission = Permission.storage;
      } else {
        permission = Permission.audio;
      }
    } else {
      permission = Permission.photos;
    }

    return _requestPermission(
      permission,
      showRequestRationaleDialog: () async {
        await _showRequestRationaleDialog(
          context,
          appLocalizations.ziiChatWouldLikeToAccessYourPhotos,
          appLocalizations.pleaseEnableYourPhotosFromTheDeviceSettings,
        );
      },
    );
  }

  static Future<bool> requestLocationPermission(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    return _requestPermission(
      Permission.locationWhenInUse,
      showRequestRationaleDialog: () async {
        await _showRequestRationaleDialog(
          context,
          appLocalizations.ziiChatWouldLikeToAccessTheLocation,
          appLocalizations.pleaseEnableYourLocationFromDeviceSettings2,
        );
      },
    );
  }

  static Future<bool> requestSpeechPermission(BuildContext context) async {
    return _requestPermission(
      Permission.speech,
      showRequestRationaleDialog: () async =>
          ui.DialogUtils.showAccessSpeechRecognitionSystemDialog(
        context,
        onNotNowClicked: (BuildContext dialogContext) {
          Navigator.of(dialogContext).pop();
        },
        onOpenSettingClicked: (BuildContext dialogContext) {
          Navigator.of(dialogContext).pop();
          openAppSettings();
        },
      ),
    );
  }

  static requestNotificationPermission(BuildContext context) {
    return _requestPermission(
      Permission.notification,
      showRequestRationaleDialog: () async => null,
    );
  }

  static Future<bool> isEnableLocation({
    bool? hasRequestPermission = true,
    bool? onlyEnableLocation = false,
  }) async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (onlyEnableLocation == true) {
      return serviceEnabled;
    }
    bool checkPermission = await PermissionUtils.checkLocationPermission(
      hasRequestPermission: hasRequestPermission,
    );
    return serviceEnabled && checkPermission;
  }

  static Future<bool> openEnableLocation() async {
    if (Platform.isAndroid) {
      if (await isEnableLocation(onlyEnableLocation: true) == false) {
        await Geolocator.openLocationSettings();
      } else {
        await Geolocator.openAppSettings();
      }
    } else {
      if (await isEnableLocation(onlyEnableLocation: true) == false) {
        return false;
      } else {
        await Geolocator.openAppSettings();
      }
    }
    return true;
  }

  static Future<bool> checkLocationPermission({
    bool? hasRequestPermission = true,
  }) async {
    LocationPermission permission;
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      if (hasRequestPermission == false) {
        return false;
      }
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }
    if (permission == LocationPermission.deniedForever) {
      return false;
    }
    return true;
  }

  static Future<Position?> getCurrentPosition() async {
    try {
      return await Geolocator.getCurrentPosition();
    } catch (error) {
      return null;
    }
  }

  static Future<String> getListAddress(
    double latitude,
    double longitude,
  ) async {
    List<Placemark> placeMarks =
        await placemarkFromCoordinates(latitude, longitude);
    String ward = "";

    if (Platform.isAndroid) {
      placeMarks.forEach((item) {
        if (item.subLocality != null && item.subLocality!.isNotEmpty) {
          ward = item.subLocality!;
        }
      });
    } else {
      ward = placeMarks.first.subLocality!;
    }
    String address = "${placeMarks.first.street}, ${ward},"
        " ${placeMarks.first.subAdministrativeArea}, "
        "${placeMarks.first.administrativeArea}";
    return address;
  }

  static streamLocationService(ValueNotifier<bool>? allowAccessLocation) {
    Geolocator.getServiceStatusStream().listen((ServiceStatus status) {
      switch (status) {
        case ServiceStatus.disabled:
          allowAccessLocation?.value = false;
        case ServiceStatus.enabled:
          // TODO: Handle this case.
          allowAccessLocation?.value = true;
      }
    });
  }

  static streamGetPosition(ValueNotifier<Position?> position) {
    _streamPosition?.cancel();
    _streamPosition =
        Geolocator.getPositionStream().listen((Position? _position) {
      position.value = _position;
    });
  }

  static close() {
    _streamPosition?.cancel();
  }
}
