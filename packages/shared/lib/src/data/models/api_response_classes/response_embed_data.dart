import 'package:json_annotation/json_annotation.dart';

part 'response_embed_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseEmbedData {
  @Json<PERSON>ey(name: 'url')
  String? url;

  @J<PERSON><PERSON><PERSON>(name: 'version')
  String? version;

  @J<PERSON><PERSON><PERSON>(name: 'title')
  String? title;

  @J<PERSON><PERSON><PERSON>(name: 'authorName')
  String? authorName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'authorUrl')
  String? authorUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'providerName')
  String? providerName;

  @<PERSON>son<PERSON><PERSON>(name: 'providerUrl')
  String? providerUrl;

  @Json<PERSON>ey(name: 'cacheAge')
  String? cacheAge;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'html')
  String? html;

  @J<PERSON><PERSON><PERSON>(name: 'width')
  int? width;

  @J<PERSON><PERSON><PERSON>(name: 'height')
  int? height;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'description')
  String? description;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailUrl')
  String? thumbnailUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailWidth')
  String? thumbnailWidth;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailHeight')
  String? thumbnailHeight;

  ResponseEmbedData({
    this.url,
    this.version,
    this.title,
    this.authorName,
    this.authorUrl,
    this.providerName,
    this.providerUrl,
    this.cacheAge,
    this.html,
    this.width,
    this.height,
    this.description,
    this.thumbnailUrl,
    this.thumbnailWidth,
    this.thumbnailHeight,
  });

  factory ResponseEmbedData.fromJson(Map<String, dynamic> json) =>
      _$ResponseEmbedDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseEmbedDataToJson(this);
}
