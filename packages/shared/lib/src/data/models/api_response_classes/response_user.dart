import 'package:json_annotation/json_annotation.dart';

import '../../../utils/string_utils.dart';
import 'response_presence_data.dart';
import 'response_profile.dart';

part 'response_user.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseUser {
  ResponseUser({
    required this.userId,
    this.username,
    this.createTime,
    this.updateTime,
    this.profile,
    this.userType,
    this.presenceData,
    this.statusData,
    this.blocked,
  });

  final String userId;
  final String? username;
  final String? createTime;
  final String? updateTime;
  final ResponseProfile? profile;
  final int? userType;
  final ResponsePresenceData? presenceData;
  final ResponseUserStatus? statusData;
  final bool? blocked;

  String get showName {
    if (!StringUtils.isNullOrEmpty(profile?.displayName)) {
      return profile!.displayName!;
    }
    return username ?? '';
  }

  factory ResponseUser.fromJson(Map<String, dynamic> json) =>
      _$ResponseUserFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseUserToJson(this);
}

@JsonSerializable(explicitToJson: true)
class ResponseUserStatus {
  final String content;
  final String? status;
  final int? expireAfterTime;
  final String? createTime;
  final String? updateTime;
  final String? endTime;

  ResponseUserStatus({
    required this.content,
    this.status,
    this.expireAfterTime,
    this.createTime,
    this.updateTime,
    this.endTime,
  });

  factory ResponseUserStatus.fromJson(Map<String, dynamic> json) =>
      _$ResponseUserStatusFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseUserStatusToJson(this);
}
