import 'package:json_annotation/json_annotation.dart';

part 'response_audio_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseAudioMetadata {
  @JsonKey(name: 'samples')
  List<int>? samples;

  ResponseAudioMetadata({
    this.samples,
  });

  factory ResponseAudioMetadata.fromJson(Map<String, dynamic> json) =>
      _$ResponseAudioMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseAudioMetadataToJson(this);
}
