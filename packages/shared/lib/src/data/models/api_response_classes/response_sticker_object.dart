import 'package:json_annotation/json_annotation.dart';

part 'response_sticker_object.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseStickerObject {
  @Json<PERSON>ey(name: 'collectionId')
  String? collectionId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'stickerId')
  String? stickerId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'attachmentType')
  int? attachmentType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'stickerUrl')
  String? stickerUrl;

  @Json<PERSON><PERSON>(name: 'attachmentId')
  String? attachmentId;

  @JsonKey(name: 'fileRef')
  String? fileRef;

  ResponseStickerObject({
    this.collectionId,
    this.stickerId,
    this.attachmentType,
    this.stickerUrl,
    this.attachmentId,
    this.fileRef,
  });

  factory ResponseStickerObject.fromJson(Map<String, dynamic> json) =>
      _$ResponseStickerObjectFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseStickerObjectToJson(this);
}
