import 'package:json_annotation/json_annotation.dart';

import '../../../../shared.dart';

part 'api_response.g.dart';

@JsonSerializable(explicitToJson: true)
class APIResponse {
  final bool ok;
  @JsonKey(fromJson: _dataFromJson, toJson: _dataToJson)
  final dynamic data;
  final Paging? paging;
  final ResponseError? error;
  final ResponseIncludes? includes;

  APIResponse({
    required this.ok,
    this.data,
    this.paging,
    this.error,
    this.includes,
  });

  factory APIResponse.fromJson(Map<String, dynamic> json) =>
      _$APIResponseFromJson(json);

  Map<String, dynamic> toJson() => _$APIResponseToJson(this);

  static dynamic _dataFromJson(Object? json) {
    if (json is List) {
      return json
          .map((e) => APIData.fromJson(e as Map<String, dynamic>))
          .toList();
    } else if (json is Map<String, dynamic>) {
      return APIData.fromJson(json);
    }
    return null;
  }

  static Object? _dataToJson(dynamic data) {
    if (data is List<APIData>) {
      return data.map((e) => e.toJson()).toList();
    } else if (data is APIData) {
      return data.toJson();
    }
    return null;
  }

  APIData? getData() {
    return data is APIData ? data as APIData : null;
  }

  List<APIData>? getDataList() {
    return data is List<APIData> ? data as List<APIData> : null;
  }
}

@JsonSerializable(explicitToJson: true)
class ResponseIncludes {
  final ResponseUser? user;
  final ResponseFriend? friend;
  final ResponseChannel? channel;
  final ResponseMember? member;
  final ResponseMessage? message;
  final ResponseMediaAttachment? attachment;
  final List<ResponseUser>? users;
  final List<ResponseFriend>? friends;
  final List<ResponseChannel>? channels;
  final List<ResponseMember>? members;
  final List<ResponseMessage>? messages;

  ResponseIncludes({
    this.user,
    this.friend,
    this.channel,
    this.member,
    this.message,
    this.attachment,
    this.users,
    this.friends,
    this.channels,
    this.members,
    this.messages,
  });

  factory ResponseIncludes.fromJson(Map<String, dynamic> json) =>
      _$ResponseIncludesFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseIncludesToJson(this);
}

extension ResponseIncludesExtension on ResponseIncludes {
  bool get hasUser => user != null;

  bool get hasFriend => friend != null;

  bool get hasChannel => channel != null;

  bool get hasMember => member != null;

  bool get hasMessage => message != null;

  bool get hasAttachment => attachment != null;

  bool get hasUsers => users?.isNotEmpty ?? false;

  bool get hasFriends => friends?.isNotEmpty ?? false;

  bool get hasChannels => channels?.isNotEmpty ?? false;

  bool get hasMembers => members?.isNotEmpty ?? false;

  bool get hasMessages => messages?.isNotEmpty ?? false;

  ResponseUser? getUser() => user;

  ResponseFriend? getFriend() => friend;

  ResponseChannel? getChannel() => channel;

  ResponseMember? getMember() => member;

  ResponseMessage? getMessage() => message;

  ResponseMediaAttachment? getAttachment() => attachment;

  List<ResponseUser>? getUsers() => users;

  List<ResponseFriend>? getFriends() => friends;

  List<ResponseChannel>? getChannels() => channels;

  List<ResponseMember>? getMembers() => members;

  List<ResponseMessage>? getMessages() => messages;

  ResponseUser? findUserById(String id) =>
      users?.firstWhere((user) => user.userId == id, orElse: null);

  ResponseFriend? findFriendById(String id) =>
      friends?.firstWhere((friend) => friend.friendId == id, orElse: null);

  ResponseChannel? findChannelById(String id) =>
      channels?.firstWhere((channel) => channel.channelId == id, orElse: null);

  ResponseMember? findMemberById(String id) =>
      members?.firstWhere((member) => member.userId == id, orElse: null);

  ResponseMessage? findMessageById(String id) =>
      messages?.firstWhere((message) => message.messageId == id, orElse: null);

  static List<ResponseUser> usersFromJson(List<dynamic> json) => json
      .map((e) => ResponseUser.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> usersToJson(List<ResponseUser> users) =>
      users.map((e) => e.toJson()).toList();

  static List<ResponseFriend> friendsFromJson(List<dynamic> json) => json
      .map((e) => ResponseFriend.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> friendsToJson(
    List<ResponseFriend> friends,
  ) =>
      friends.map((e) => e.toJson()).toList();

  static List<ResponseChannel> channelsFromJson(List<dynamic> json) => json
      .map((e) => ResponseChannel.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> channelsToJson(
    List<ResponseChannel> channels,
  ) =>
      channels.map((e) => e.toJson()).toList();

  static List<ResponseMember> membersFromJson(List<dynamic> json) => json
      .map((e) => ResponseMember.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> membersToJson(
    List<ResponseMember> members,
  ) =>
      members.map((e) => e.toJson()).toList();

  static List<ResponseMessage> messagesFromJson(List<dynamic> json) => json
      .map((e) => ResponseMessage.fromJson(e as Map<String, dynamic>))
      .toList();

  static List<Map<String, dynamic>> messagesToJson(
    List<ResponseMessage> messages,
  ) =>
      messages.map((e) => e.toJson()).toList();

  static List<ResponseMediaAttachment> attachmentsFromJson(
    List<dynamic> json,
  ) =>
      json
          .map(
            (e) => ResponseMediaAttachment.fromJson(e as Map<String, dynamic>),
          )
          .toList();

  static List<Map<String, dynamic>> attachmentsToJson(
    List<ResponseMediaAttachment> attachments,
  ) =>
      attachments.map((e) => e.toJson()).toList();
}
