import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/database/entities/sticker.dart';
import '../../data/repositories/interfaces/sticker_repository.dart';

@Injectable()
class GetStickerUseCase
    extends BaseFutureUseCase<GetStickerInput, GetStickerOutput> {
  const GetStickerUseCase(this._repository);

  final StickerRepository _repository;

  @protected
  @override
  Future<GetStickerOutput> buildUseCase(
    GetStickerInput input,
  ) async {
    final sticker = _repository.getByStickerId(input.stickerId);
    return GetStickerOutput(sticker: sticker);
  }
}

class GetStickerInput extends BaseInput {
  final String stickerId;

  GetStickerInput({
    required this.stickerId,
  });
}

class GetStickerOutput extends BaseOutput {
  final Sticker? sticker;

  GetStickerOutput({
    required this.sticker,
  });
}
