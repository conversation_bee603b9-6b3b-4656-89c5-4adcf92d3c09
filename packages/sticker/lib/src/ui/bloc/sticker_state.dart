part of 'sticker_bloc.dart';

enum CollectionLoadingStatus { initial, loading, loaded, error }

class CollectionState {
  final CollectionLoadingStatus status;
  final String? errorMessage;
  final DateTime lastUpdated;

  CollectionState({
    this.status = CollectionLoadingStatus.initial,
    this.errorMessage,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  CollectionState copyWith({
    CollectionLoadingStatus? status,
    String? errorMessage,
    DateTime? lastUpdated,
  }) {
    return CollectionState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@freezed
sealed class StickerState extends BaseBlocState with _$StickerState {
  const StickerState._();

  factory StickerState({
    @Default([]) List<Collection> collections,
    @Default({}) Map<String, String> thumbnailPaths,
    @Default({}) Map<String, String> stickerPaths,
    @Default({}) Map<String, CollectionState> collectionsState,
    @Default(true) bool isLoading,
    String? stickerOnPreview,
  }) = _StickerState;

  String? pathSpriteOfSticker(String url) {
    return stickerPaths[url];
  }

  String? pathOfThumbnail(String url) {
    return thumbnailPaths[url];
  }

  CollectionState getCollectionState(String collectionId) {
    return collectionsState[collectionId] ??
        CollectionState(status: CollectionLoadingStatus.initial);
  }

  bool isCollectionLoading(String collectionId) {
    final state = getCollectionState(collectionId);
    return state.status == CollectionLoadingStatus.loading;
  }

  bool isCollectionLoaded(String collectionId) {
    final state = getCollectionState(collectionId);
    return state.status == CollectionLoadingStatus.loaded;
  }

  bool hasCollectionError(String collectionId) {
    final state = getCollectionState(collectionId);
    return state.status == CollectionLoadingStatus.error;
  }

  String? getCollectionErrorMessage(String collectionId) {
    final state = getCollectionState(collectionId);
    return state.status == CollectionLoadingStatus.error
        ? state.errorMessage
        : null;
  }
}
