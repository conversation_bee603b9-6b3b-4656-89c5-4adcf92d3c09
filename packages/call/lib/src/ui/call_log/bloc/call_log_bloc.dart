import 'dart:async';
import 'dart:math';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../domain/models/call_type.dart';
import '../../../domain/models/call_user.dart';
import '../../../domain/usecase/create_call_use_case.dart';

part 'call_log_bloc.freezed.dart';
part 'call_log_event.dart';
part 'call_log_state.dart';

@injectable
class CallLogBloc extends Bloc<CallLogEvent, CallLogState> {
  CallLogBloc(
    this._privateDataRepository,
    this._deleteCallLogUseCase,
    this._createCallUseCase,
    this._createCallLogUseCase,
    this._webSocketManager,
    this._getStreamUsersUseCase,
  ) : super(CallLogState.initial()) {
    on<Initiate>(_onInit);
    on<UpdateCallLogList>(_onUpdateCallLogList);
    on<DeleteCallLog>(_onDeleteCallLog);
    on<CreateCall>(_onCreateNewCall);
    on<UpdateStreamUsers>(_onUpdateStreamUsers);
    on<UpdateUsers>(_onUpdateUsers);
  }

  final PrivateDataRepository _privateDataRepository;
  final DeleteCallLogUseCase _deleteCallLogUseCase;
  final CreateCallUseCase _createCallUseCase;
  final CreateCallLogUseCase _createCallLogUseCase;
  final GetStreamUsersUseCase _getStreamUsersUseCase;

  final WebSocketManager _webSocketManager;

  StreamSubscription? _callLogSubscription;
  StreamSubscription? _usersSubscription;

  @override
  Future<void> close() {
    _callLogSubscription?.cancel();
    _usersSubscription?.cancel();
    return super.close();
  }

  void _onInit(
    Initiate event,
    Emitter<CallLogState> emit,
  ) {
    _callLogSubscription?.cancel();
    _callLogSubscription =
        _privateDataRepository.getStreamCallLogs().listen((callLogs) {
      add(UpdateCallLogList(callLogs: callLogs));
    });
  }

  void _onUpdateCallLogList(
    UpdateCallLogList event,
    Emitter<CallLogState> emit,
  ) {
    emit(CallLogState.callLogsLoaded(event.callLogs));
  }

  void _onUpdateStreamUsers(
    UpdateStreamUsers event,
    Emitter<CallLogState> emit,
  ) {
    final output = _getStreamUsersUseCase
        .execute(GetStreamUsersInput(userIds: event.userIds));
    if (output.streamUsers == null) return;
    _usersSubscription?.cancel();
    _usersSubscription = output.streamUsers!.listen((mapUsers) {
      add(
        UpdateUsers(
          mapCallUsers: {
            for (var map in mapUsers)
              map['userId']: CallUser(
                userId: map['userId'],
                username: map['username'],
                displayName: map['profile']['displayName'],
                avatar: map['profile']['avatar'],
              ),
          },
        ),
      );
    });
  }

  void _onUpdateUsers(
    UpdateUsers event,
    Emitter<CallLogState> emit,
  ) {
    emit(CallLogState.usersLoaded(event.mapCallUsers));
  }

  Future<void> _onDeleteCallLog(
    DeleteCallLog event,
    Emitter<CallLogState> emit,
  ) async {
    final output = await _deleteCallLogUseCase
        .execute(DeleteCallLogInput(callId: event.callId));

    if (output.cloudEvent != null) {
      _webSocketManager.sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _onCreateNewCall(
    CreateCall event,
    Emitter<CallLogState> emit,
  ) async {
    final callId = UUIDUtils.random();
    _createCallUseCase.execute(
      CreateCallInput(
        callId: callId,
        userId: event.userId,
        callType: event.type,
      ),
    );
    // TODO: Dummy data
    final endReasonRandom = Random().nextInt(5);
    final timeCallRandom = Random().nextInt(20000);
    final output = await _createCallLogUseCase.execute(
      CreateCallLogInput(
        callLog: CallLogPrivateData(
          sessionKey: Config.getInstance().activeSessionKey!,
          callId: callId,
          callerId: Config.getInstance().activeSessionKey!,
          calleeId: event.userId,
          callType: event.type == CallType.VIDEO ? 1 : 2,
          callTimeInSeconds: (endReasonRandom == 2 || endReasonRandom == 4)
              ? timeCallRandom
              : 0,
          createTime: TimeUtils.getCurrentUTCTimeFormatted(),
          endedReason: endReasonRandom,
          isOutgoing: true,
        ),
      ),
    );
    _webSocketManager.sendMessage(output.cloudEvent!);

    event.onCallCreated?.call();
  }
}
