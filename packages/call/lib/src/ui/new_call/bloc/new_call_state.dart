part of 'new_call_bloc.dart';

@freezed
sealed class NewCallState with _$NewCallState {
  const NewCallState._();

  factory NewCallState.initial() = NewCallStateInitial;

  factory NewCallState.loaded({required List<CallUser> callUsers}) =
      NewCallStateLoaded;

  factory NewCallState.filtered({required List<CallUser> callUsers}) =
      NewCallStateFiltered;
}

extension NewCallStateX on NewCallState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<CallUser> callUsers)? loaded,
    T Function(List<CallUser> callUsers)? filtered,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is NewCallStateInitial && initial != null) return initial();
    if (state is NewCallStateLoaded && loaded != null) {
      return loaded(state.callUsers);
    }
    if (state is NewCallStateFiltered && filtered != null) {
      return filtered(state.callUsers);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<CallUser> callUsers) loaded,
    required T Function(List<CallUser> callUsers) filtered,
  }) {
    final state = this;

    if (state is NewCallStateInitial) return initial();
    if (state is NewCallStateLoaded) return loaded(state.callUsers);
    if (state is NewCallStateFiltered) return filtered(state.callUsers);

    throw StateError('Unhandled state: $state');
  }
}
