import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';

import '../../data/repositories/database/database.dart';
import '../../data/repositories/database/generated/objectbox.g.dart';
import 'di.config.dart';

final GetIt getIt = GetIt.instance;

@injectableInit
Future<void> configureInjection() async {
  final dir = await getApplicationSupportDirectory();

  final callStore =
      CallStore(getObjectBoxModel(), directory: '${dir.path}/call_store');
  CallDatabase(callStore);
  getIt
    ..registerSingleton<CallStore>(callStore)
    ..init();
}
