import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_profile_api/user_profile_api.dart';

import '../../../user_manager.dart';

part 'update_cover_photo_use_case.freezed.dart';

@Injectable()
class UpdateCoverPhotoUseCase
    extends BaseFutureUseCase<UpdateCoverPhotoInput, UpdateCoverPhotoOutput> {
  UpdateCoverPhotoUseCase();

  @protected
  @override
  Future<UpdateCoverPhotoOutput> buildUseCase(
    UpdateCoverPhotoInput input,
  ) async {
    final body = V3UpdateCoverPhotoRequestBuilder()
      ..coverPath = input.coverPath;

    final builtBody = body.build();

    final response = await UserProfileClient().instance.updateCoverPhoto(
          body: builtBody,
        );

    if (response.data?.ok ?? false) {
      final coverData = response.data!.data!;

      return UpdateCoverPhotoOutput(
        success: true,
        coverPath: coverData.cover,
      );
    }
    return UpdateCoverPhotoOutput(
      success: false,
      error: response.data?.error as V3Error,
    );
  }
}

@freezed
sealed class UpdateCoverPhotoInput extends BaseInput
    with _$UpdateCoverPhotoInput {
  const UpdateCoverPhotoInput._();
  factory UpdateCoverPhotoInput({
    required String? coverPath,
  }) = _UpdateCoverPhotoInput;
}

@freezed
sealed class UpdateCoverPhotoOutput extends BaseOutput
    with _$UpdateCoverPhotoOutput {
  const UpdateCoverPhotoOutput._();
  factory UpdateCoverPhotoOutput({
    required bool success,
    String? coverPath,
    final V3Error? error,
  }) = _UpdateCoverPhotoOutput;
}
