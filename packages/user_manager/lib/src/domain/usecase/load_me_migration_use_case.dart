import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_view_api/user_view_api.dart';

import '../../../user_manager.dart';

@Injectable()
class LoadMeMigrationUseCase
    extends BaseFutureUseCase<LoadMeMigrationInput, LoadMeMigrationOutput> {
  const LoadMeMigrationUseCase();

  @protected
  @override
  Future<LoadMeMigrationOutput> buildUseCase(
    LoadMeMigrationInput input,
  ) async {
    try {
      final result = await UserViewClient().instance.getMe(
        headers: {
          'x-session-token': input.token,
        },
      );
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3Me.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = json['userId'];
        final user = User.fromJson(json);
        return LoadMeMigrationOutput(user: user, rawUser: result.data!.data);
      }
      return LoadMeMigrationOutput(user: null);
    } on Exception catch (_) {
      return LoadMeMigrationOutput(user: null);
    }
  }
}

class LoadMeMigrationInput extends BaseInput {
  final String token;

  LoadMeMigrationInput(this.token);
}

class LoadMeMigrationOutput extends BaseOutput {
  final User? user;
  final V3Me? rawUser;

  LoadMeMigrationOutput({this.user, this.rawUser});
}
