import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class UpsertFriendUseCase
    extends BaseFutureUseCase<UpsertFriendInput, UpsertFriendOutput> {
  const UpsertFriendUseCase(this._friendRepository);

  final FriendRepository _friendRepository;

  @protected
  @override
  Future<UpsertFriendOutput> buildUseCase(UpsertFriendInput input) async {
    final friends = input.friends
        .map(
          (friend) =>
              friend..sessionKey = Config.getInstance().activeSessionKey ?? '',
        )
        .toList();

    List<int> ids = await _friendRepository.insertAll(friends);

    return UpsertFriendOutput(total: ids.length);
  }
}

class UpsertFriendInput extends BaseInput {
  final List<Friend> friends;

  UpsertFriendInput({required this.friends});
}

class UpsertFriendOutput extends BaseOutput {
  final int total;

  UpsertFriendOutput({required this.total});
}
