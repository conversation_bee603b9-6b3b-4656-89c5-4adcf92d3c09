import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_profile_api/user_profile_api.dart';

import '../../../user_manager.dart';

part 'set_display_name_use_case.freezed.dart';

@Injectable()
class SetDisplayNameUseCase
    extends BaseFutureUseCase<SetDisplayNameInput, SetDisplayNameOutput> {
  SetDisplayNameUseCase();

  final userId = Config.getInstance().activeSessionKey ?? '';

  @protected
  @override
  Future<SetDisplayNameOutput> buildUseCase(
    SetDisplayNameInput input,
  ) async {
    final body = V3UpdateUserDisplayNameRequestBuilder()
      ..displayName = input.displayName;
    final response = await UserProfileClient()
        .instance
        .updateUserDisplayName(body: body.build());

    if (response.data?.ok ?? false) {
      AppEventBus.publish(MeInfoUpdatedEvent());
      return SetDisplayNameOutput(
        success: true,
      );
    }
    return SetDisplayNameOutput(
      success: false,
      error: response.data?.error as V3Error,
    );
  }
}

@freezed
sealed class SetDisplayNameInput extends BaseInput with _$SetDisplayNameInput {
  const SetDisplayNameInput._();
  factory SetDisplayNameInput({required String displayName}) =
      _SetDisplayNameInput;
}

@freezed
sealed class SetDisplayNameOutput extends BaseOutput
    with _$SetDisplayNameOutput {
  const SetDisplayNameOutput._();
  factory SetDisplayNameOutput({
    required bool success,
    final V3Error? error,
  }) = _SetDisplayNameOutput;
}
