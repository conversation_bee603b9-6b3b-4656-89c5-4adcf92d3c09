# ObjectBox Admin Configuration Guide

This guide explains how ObjectBox Admin has been configured in your Flutter Data Router project, based on the [official ObjectBox documentation](https://docs.objectbox.io/data-browser#admin-for-android).

## ✅ Configuration Status: COMPLETE

Your ObjectBox Admin is **fully configured and ready to use**! Here's what has been set up:

### 1. Android Configuration (✅ Complete)

**File: `example/android/app/build.gradle.kts`**

```kotlin
dependencies {
    val objectboxVersion = "4.2.0"
    
    // ✅ Debug builds get ObjectBox with Admin web interface
    debugImplementation("io.objectbox:objectbox-android-objectbrowser:$objectboxVersion")
    
    // ✅ Release builds get regular ObjectBox without Admin (production-safe)
    releaseImplementation("io.objectbox:objectbox-android:$objectboxVersion")
}

configurations {
    getByName("debugImplementation") {
        // ✅ Exclude regular ObjectBox library for debug builds to avoid conflicts
        exclude(group = "io.objectbox", module = "objectbox-android")
    }
}
```

### 2. Flutter/Dart Configuration (✅ Complete)

**File: `lib/src/core/data_router_database.dart`**

The `DataRouterDatabase` class now includes:

- ✅ Admin instance management
- ✅ Automatic initialization in debug mode
- ✅ Proper cleanup on database close
- ✅ Admin availability checking

```dart
class DataRouterDatabase {
  Store? _store;
  Admin? _admin;  // ✅ Admin instance
  
  /// Get the ObjectBox Admin instance (only available in debug mode)
  Admin? get admin => _admin;
  
  /// Initialize ObjectBox Admin for debug builds
  void _initializeAdmin() {
    if (kDebugMode && Admin.isAvailable()) {
      try {
        _admin = Admin(store, bindUri: 'http://127.0.0.1:8090');
        print('✅ ObjectBox Admin started at http://127.0.0.1:8090');
      } catch (e) {
        print('❌ Failed to start ObjectBox Admin: $e');
      }
    }
  }
}
```

## 🚀 How to Use ObjectBox Admin

### Step 1: Run Your App in Debug Mode

```bash
# Run the example app in debug mode
cd example
flutter run --debug
```

### Step 2: Check Admin Status

When the app starts, you should see this message in the console:

```
✅ ObjectBox Admin started at http://127.0.0.1:8090
```

### Step 3: Access the Web Interface

#### Option A: On Device/Emulator
- Open a web browser on your device/emulator
- Navigate to: `http://127.0.0.1:8090`

#### Option B: On Development Machine
1. Set up port forwarding:
   ```bash
   adb forward tcp:8090 tcp:8090
   ```

2. Open your web browser and navigate to:
   ```
   http://127.0.0.1:8090
   ```

### Step 4: Explore Your Data

The ObjectBox Admin web interface allows you to:

- 📊 **View Database Schema**: See all your entity definitions
- 🔍 **Browse Data**: View all objects in your database
- 📈 **Monitor Statistics**: Check database performance metrics
- 💾 **Download Data**: Export objects in JSON format
- 🔄 **Real-time Updates**: See changes as they happen

## 🎯 What You'll See

### Database Schema
- View your `SessionEntity` structure
- See property types and relationships
- Understand your data model visually

### Data Browser
- Browse all sessions in your database
- Filter and search through records
- View individual object details

### Statistics
- Database size and performance metrics
- Object counts per entity type
- Query performance information

## 🛡️ Security & Production

### Automatic Safety Features

✅ **Debug-Only**: Admin only runs in debug builds (`kDebugMode`)
✅ **No Production Impact**: Release builds use regular ObjectBox without Admin
✅ **Local Only**: Admin runs locally, no cloud/external connections
✅ **Automatic Permissions**: Required permissions only added to debug builds

### Permissions Added (Debug Only)

The `objectbox-android-objectbrowser` dependency automatically adds these permissions to debug builds:

```xml
<!-- Required to provide the web interface -->
<uses-permission android:name="android.permission.INTERNET" />
<!-- Required to run keep-alive service when targeting API 28 or higher -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
<!-- When targeting API level 33 or higher to post the initial Admin notification -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
```

**Important**: These permissions are **NOT** added to release builds.

## 🔧 Troubleshooting

### Admin Not Starting?

1. **Check Debug Mode**: Admin only works in debug builds
   ```bash
   flutter run --debug  # ✅ Correct
   flutter run --release # ❌ Admin won't start
   ```

2. **Check Console Output**: Look for admin startup messages
   ```
   ✅ ObjectBox Admin started at http://127.0.0.1:8090
   ```

3. **Verify Dependencies**: Ensure your `build.gradle.kts` has the correct configuration

### Can't Access Web Interface?

1. **Port Forwarding**: Make sure ADB port forwarding is set up
   ```bash
   adb forward tcp:8090 tcp:8090
   ```

2. **Check URL**: Use the exact URL shown in console output
   ```
   http://127.0.0.1:8090
   ```

3. **Firewall**: Ensure your firewall isn't blocking the connection

### Different Port Needed?

If port 8090 is already in use, you can change it:

```dart
_admin = Admin(store, bindUri: 'http://127.0.0.1:8091'); // Use port 8091
```

## 📱 Example App Demo

The included example app (`example/lib/database_demo.dart`) demonstrates:

- ✅ Database initialization with Admin
- ✅ Admin status display
- ✅ Creating and viewing test data
- ✅ Real-time data updates in Admin interface

Run the example to see ObjectBox Admin in action!

## 🎉 Next Steps

1. **Run the Example**: Test the configuration with the included demo
2. **Create Test Data**: Use the "Create Test Session" button to add data
3. **Explore Admin Interface**: Browse your data in the web interface
4. **Monitor Real-time**: Watch data changes happen live in the Admin
5. **Download Data**: Export your data in JSON format for analysis

Your ObjectBox Admin is now fully configured and ready to help you debug and monitor your database! 🚀
